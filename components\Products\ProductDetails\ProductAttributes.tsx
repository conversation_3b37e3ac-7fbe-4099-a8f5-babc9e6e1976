import { useState } from "react";

export default function ProductAttributes() {
  const [attributes, setAttributes] = useState([
    { title: "", value: "" } 
  ]);

  const handleAddAttribute = () => {
    setAttributes([...attributes, { title: "", value: "" }]);
  };

  const handleChange = (index: number, field: "title" | "value", newValue: string) => {
    const updated = [...attributes];
    updated[index][field] = newValue;
    setAttributes(updated);
  };

  return (
    <div className="flex flex-col gap-7 mt-5">
      <div className="flex justify-between items-center">
        <h2>ویژگی های محصول</h2>
        <button
          type="button"
          onClick={handleAddAttribute}
          className="bg-muted-foreground text-white p-2 px-3 rounded-lg !cursor-pointer"
        >
          افزودن ویژگی
        </button>
      </div>

      {attributes.map((attr, index) => (
        <div key={index} className="flex md:gap-5 max-md:justify-between">
          <div className="flex flex-col gap-3 max-md:w-[45%]">
            <label className="px-1">عنوان ویژگی</label>
            <input
              placeholder="مثال: وزن"
              type="text"
              value={attr.title}
              onChange={(e) => handleChange(index, "title", e.target.value)}
              className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
            />
          </div>
          <div className="flex flex-col gap-3 w-full max-md:w-[45%]">
            <label className="px-1">مقدار ویژگی</label>
            <input
              placeholder="مثال: 1000 گرم"
              type="text"
              value={attr.value}
              onChange={(e) => handleChange(index, "value", e.target.value)}
              className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
            />
          </div>
        </div>
      ))}

      
    </div>
  );
}
