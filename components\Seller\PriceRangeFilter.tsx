"use client";

import { Dispatch, SetStateAction, useMemo, useState, useEffect } from "react";
import Slider from "rc-slider";
import "rc-slider/assets/index.css";
// import { ProductFilterOptions } from "@/lib/types/product.types";
import { debounce } from "@/lib/debounce";

type Props = {
  min: number;
  max: number;
  minRange?: number;
  maxRange?: number;
  setProductParams?: Dispatch<SetStateAction<ProductFilterOptions>>;
};

const PriceRangeFilter = ({
  min,
  max,
  minRange,
  maxRange,
  setProductParams,
}: Props) => {
  const [range, setRange] = useState<[number, number]>([
    minRange ?? min,
    maxRange ?? max,
  ]);

  useEffect(() => {
    setRange([minRange ?? min, maxRange ?? max]);
  }, [minRange, maxRange, min, max]);

  const debouncedSetParams = useMemo(() => {
    return debounce((val: [number, number]) => {
      setProductParams?.((prev) => ({
        ...prev,
        min_price: val[0],
        max_price: val[1],
        page: 1,
      }));
    }, 1000);
  }, [setProductParams]);

  const handleChange = (val: number | number[]) => {
    const newRange = val as [number, number];
    setRange(newRange);
    debouncedSetParams(newRange);
  };

  const formatPrice = (price: number) =>
    new Intl.NumberFormat("fa-IR").format(price);

  const parseFormattedPrice = (value: string) =>
    Number(value.replace(/,/g, ""));

  return (
    <div className="w-full max-w-xs mt-5 bg-white rounded-2xl rtl">
      <div className="flex flex-col gap-2">
        {/* Min Price */}
        <div className="flex items-center justify-between p-2 rounded-lg">
          <span className="text-gray-500 text-sm mmax-md:text-xs">از</span>
          <div className="w-[70%] bg-[#f5f6f8] flex justify-between border-2 border-gray-100 rounded-2xl overflow-hidden text-sm">
            <input
              name="minPrice"
              onChange={(e) =>
                handleChange([parseFormattedPrice(e.target.value), range[1]])
              }
              value={formatPrice(range[0])}
              type="text"
              inputMode="numeric"
              className="h-full outline-0 ring-0 bg-[#f5f6f8] p-3 w-[70%] text-sm max-md:text-xs overflow-hidden"
            />
            <span className="text-gray-500 p-3 max-md:text-xs text-sm font-semibold bg-white rounded-br-xl">
              تومان
            </span>
          </div>
        </div>

        {/* Max Price */}
        <div className="flex items-center justify-between p-2 rounded-lg">
          <span className="text-gray-500 text-sm mmax-md:text-xs">تا</span>
          <div className="w-[70%] bg-[#f5f6f8] flex justify-between border-2 border-gray-100 rounded-2xl overflow-hidden text-sm">
            <input
              name="maxPrice"
              onChange={(e) =>
                handleChange([range[0], parseFormattedPrice(e.target.value)])
              }
              value={formatPrice(range[1])}
              type="text"
              inputMode="numeric"
              className="h-full outline-0 ring-0 bg-[#f5f6f8] p-3 w-[70%] text-sm max-md:text-xs overflow-hidden"
            />
            <span className="text-gray-500 p-3 max-md:text-xs text-sm font-semibold bg-white rounded-br-xl">
              تومان
            </span>
          </div>
        </div>
      </div>

      {/* Dual Handle Range Slider */}
      <div className="mt-4 px-2">
        <Slider
          range
          min={min}
          reverse={true}
          max={max}
          step={10000}
          value={range}
          onChange={handleChange}
          trackStyle={{ backgroundColor: "#2563eb", height: 6 }}
          railStyle={{ backgroundColor: "#e5e7eb", height: 6 }}
          handleStyle={[
            {
              backgroundColor: "#2563eb",
              borderColor: "#2563eb",
              width: 16,
              height: 16,
            },
            {
              backgroundColor: "#facc15",
              borderColor: "#facc15",
              width: 16,
              height: 16,
            },
          ]}
        />
      </div>

      {/* Labels */}
      <div className="flex justify-between text-sm text-gray-500 mt-2">
        <span className="max-md:text-xs font-light max-md:font-bold text-gray-400">
          ارزانترین
        </span>
        <span className="max-md:text-xs font-light max-md:font-bold text-gray-400">
          گرانترین
        </span>
      </div>
    </div>
  );
};

export default PriceRangeFilter;
