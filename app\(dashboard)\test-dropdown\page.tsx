import CategoryDropdownExample from "@/components/common/CategoryDropdownExample";

/**
 * Test page for the new modal dropdown functionality
 * This page demonstrates the hierarchical category selection
 */
export default function TestDropdownPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            تست عملکرد DropdownSearch با مودال
          </h1>
          <p className="text-gray-600">
            این صفحه برای تست عملکرد جدید انتخاب دسته‌بندی با مودال طراحی شده است
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-lg p-8">
          <CategoryDropdownExample />
        </div>

        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            ویژگی‌های جدید:
          </h3>
          <ul className="space-y-2 text-blue-800">
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>مودال تمام صفحه برای انتخاب دسته‌بندی</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>جستجو در تمام دسته‌بندی‌ها و زیرمجموعه‌ها</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>ناوبری سلسله مراتبی با breadcrumb</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>بستن خودکار مودال پس از انتخاب نهایی</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>سازگار با RTL و فارسی</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
