"use client"
import React, { use<PERSON><PERSON>back, useEffect, useReducer, useRef, useState } from 'react';
import toast from 'react-hot-toast';
// import { updateUserAddressAction, createUserAddressAction } from '@/actions/userAddress.action';
// import { Button } from '@/components/UI/button';
// import { Label } from '@radix-ui/react-dropdown-menu';
// import { Input } from '@/components/UI/input';
import 'leaflet/dist/leaflet.css';
// import type { UserAddress } from '@/lib/types/types';
// import { UserProvider } from '@/lib/providers/UserProvider';
// import { useAuth } from '@/lib/hooks/useAuth';

// Default center: Tehran
const defaultPosition: [number, number] = [35.6892, 51.3890]; // lat, lng
const MAP_KEY = 'web.4ef75c4009054499853b98f436cde308'; // For map tiles
const SERVICE_KEY = 'service.59985951cfc749598c567adae5367fe4'; // For reverse geocoding API

// Disable TypeScript strict checks for map integration
/* eslint-disable @typescript-eslint/no-explicit-any */
export interface UserAddress {
  id: string;
  name: string;
  receiver_name: string;
  receiver_phone: string;
  is_recipient_self: boolean;
  province: string;
  city: string;
  zip_code: number;
  address: string;
  latitude: number;
  longitude: number;
}

interface AddressFormData {
  id?: string;
  name: string;
  receiver_name: string;
  receiver_phone: string;
  is_recipient_self: boolean;
  province: string;
  city: string;
  zip_code: string;
  address: string;
  latitude: number;
  longitude: number;
}

interface ValidationErrors {
  name?: string;
  receiver_name?: string;
  receiver_phone?: string;
  province?: string;
  city?: string;
  zip_code?: string;
  address?: string;
}

interface UIState {
  isReverseGeocodingInProgress: boolean;
  isAddressChangeInProgress: boolean;
  position: [number, number];
}

interface AddressModalState {
  formData: AddressFormData;
  uiState: UIState;
}

type Action =
  | { type: 'UPDATE_FIELD'; field: keyof AddressFormData; value: string | number | boolean }
  | { type: 'UPDATE_POSITION'; position: [number, number] }
  | { type: 'SET_REVERSE'; inProgress: boolean }
  | { type: 'SET_FORWARD'; inProgress: boolean }
  | { type: 'UPDATE_ADDRESS'; province: string; city: string; address: string }
  | { type: 'RESET' };

const initialState: AddressModalState = {
  formData: {
    name: '',
    receiver_name: '',
    receiver_phone: '',
    is_recipient_self: false,
    province: '',
    city: '',
    zip_code: '',
    address: '',
    latitude: defaultPosition[0],
    longitude: defaultPosition[1],
  },
  uiState: {
    isReverseGeocodingInProgress: false,
    isAddressChangeInProgress: false,
    position: defaultPosition,
  }
};

function reducer(state: AddressModalState, action: Action): AddressModalState {
  switch (action.type) {
    case 'UPDATE_FIELD':
      return {
        ...state,
        formData: { ...state.formData, [action.field]: action.value }
      };
    case 'UPDATE_POSITION':
      return {
        ...state,
        formData: { ...state.formData, latitude: action.position[0], longitude: action.position[1] },
        uiState: { ...state.uiState, position: action.position }
      };
    case 'SET_REVERSE':
      return { ...state, uiState: { ...state.uiState, isReverseGeocodingInProgress: action.inProgress } };
    case 'SET_FORWARD':
      return { ...state, uiState: { ...state.uiState, isAddressChangeInProgress: action.inProgress } };
    case 'UPDATE_ADDRESS':
      console.log('🔄 Updating address in reducer:', {
        province: action.province,
        city: action.city,
        address: action.address
      });
      const newState = {
        ...state,
        formData: {
          ...state.formData,
          province: action.province,
          city: action.city,
          address: action.address
        }
      };
      console.log('📝 New state after address update:', newState.formData);
      return newState;
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

const NeshanAddressForm: React.FC<{
  // onClose: () => void;
  onAddressCreated?: (id?: string) => void;
  addressToEdit?: UserAddress | null;
}> = ({ onAddressCreated, addressToEdit }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const mapRef = useRef<any>(null);
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const markerRef = useRef<any>(null);
  // const { userData } = useAuth()
  const [loading, setLoading] = useState(false)
  // Prefill when editing
  useEffect(() => {
    if (addressToEdit) {
      dispatch({ type: 'RESET' });
      Object.entries(addressToEdit).forEach(([field, val]) => {
        if (['latitude', 'longitude'].includes(field)) return;
        dispatch({ type: 'UPDATE_FIELD', field: field as keyof AddressFormData, value: val as string | number | boolean });
      });
      dispatch({ type: 'UPDATE_POSITION', position: [addressToEdit.latitude, addressToEdit.longitude] });
    }
  }, [addressToEdit]);

  const { formData, uiState } = state;
  const { position, isReverseGeocodingInProgress } = uiState;

  const updateField = useCallback((field: keyof AddressFormData, value: string | number | boolean) => {
    dispatch({ type: 'UPDATE_FIELD', field, value });
    // if the reciver is is user, fill the phone input with user info api
    // if (field === 'is_recipient_self') {
    //   dispatch({ type: 'UPDATE_FIELD', field: 'receiver_phone', value: value ? userData?.phone || '' : '' });
    //   dispatch({ type: 'UPDATE_FIELD', field: 'receiver_name', value: value ? userData?.fullName || '' : ''  });
    // }
    // Clear error for this field when user starts typing
    if (hasSubmitted && errors[field as keyof ValidationErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  }, [hasSubmitted, errors]);

  /**
   * Handles zip code input change - only allows numbers
   * @param e - Input change event
   */
  const handleZipCodeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers and limit to 10 characters
    const numericValue = value.replace(/[^\d]/g, '').slice(0, 10);
    updateField('zip_code', numericValue);
  }, [updateField]);

  /**
   * Handles phone number input change - only allows numbers
   * @param e - Input change event
   */
  const handlePhoneChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers and limit to 11 characters
    const numericValue = value.replace(/[^\d]/g, '').slice(0, 11);
    updateField('receiver_phone', numericValue);
  }, [updateField]);

  /**
   * Validates all form fields and returns validation errors
   * @returns ValidationErrors object with error messages
   */
  const validateForm = useCallback((): ValidationErrors => {
    const newErrors: ValidationErrors = {};

    // Name validation
    // if (!formData.name.trim()) {
    //   newErrors.name = 'نام آدرس الزامی است';
    // }

    // Province validation
    if (!formData.province.trim()) {
      newErrors.province = 'استان الزامی است';
    }

    // City validation
    if (!formData.city.trim()) {
      newErrors.city = 'شهر الزامی است';
    }

    // Zip code validation - exactly 10 characters
    if (!formData.zip_code.trim()) {
      newErrors.zip_code = 'کد پستی الزامی است';
    } else if (formData.zip_code.trim().length !== 10) {
      newErrors.zip_code = 'کد پستی باید دقیقاً ۱۰ رقم باشد';
    } else if (!/^\d{10}$/.test(formData.zip_code.trim())) {
      newErrors.zip_code = 'کد پستی باید فقط شامل اعداد باشد';
    }

    // Address validation
    if (!formData.address.trim()) {
      newErrors.address = 'آدرس کامل الزامی است';
    }

    // Receiver name validation
    if (!formData.receiver_name.trim()) {
      newErrors.receiver_name = 'نام گیرنده الزامی است';
    }

    // Receiver phone validation
    if (!formData.receiver_phone.trim()) {
      newErrors.receiver_phone = 'شماره تماس گیرنده الزامی است';
    } else if (!/^09\d{9}$/.test(formData.receiver_phone.trim())) {
      newErrors.receiver_phone = 'شماره تماس باید با ۰۹ شروع شده و ۱۱ رقم باشد';
    }

    return newErrors;
  }, [formData]);

  // Reverse geocoding
  const handleLocationSelected = useCallback(async (lat: number, lng: number) => {
    setLoading(true);
    const newPosition: [number, number] = [lat, lng];
    dispatch({ type: 'UPDATE_POSITION', position: newPosition });
    dispatch({ type: 'SET_REVERSE', inProgress: true });

    console.log('🔍 Starting reverse geocoding for:', { lat, lng });
    console.log('🔑 Using SERVICE API key:', SERVICE_KEY);
    console.log('🌐 API URL:', `https://api.neshan.org/v4/reverse?lat=${lat}&lng=${lng}`);

    try {
      const res = await fetch(`https://api.neshan.org/v4/reverse?lat=${lat}&lng=${lng}`, {
        headers: { 'Api-Key': SERVICE_KEY }
      });
      console.log('📡 Response status:', res.status, res.statusText);

      if (!res.ok) {
        console.error('❌ API request failed:', res.status, res.statusText);
        throw new Error(`API request failed: ${res.status} ${res.statusText}`);
      }

      const data = await res.json();
      console.log('📍 Reverse geocoding response:', data);

      if (data.status === 'OK' && data.formatted_address) {
        const parts = data.formatted_address.split('،').map((p: string) => p.trim());


        console.log('🏠 Parsed address parts:', {
          fullAddress: data.formatted_address,
          parts,
          province: data.state,
          city: data.city,
          address: data.formatted_address
        });

        dispatch({ type: 'UPDATE_ADDRESS', province: data.state, city: data.city, address: data.formatted_address });
        console.log('✅ Address updated in state');
      } else {
        console.warn('❌ Invalid response from reverse geocoding:', data);
        // Fallback: at least update coordinates
        dispatch({ type: 'UPDATE_FIELD', field: 'latitude', value: lat });
        dispatch({ type: 'UPDATE_FIELD', field: 'longitude', value: lng });
      }
    } catch (e) {
      console.error('💥 Reverse geocoding error:', e);
      // Fallback: at least update coordinates
      dispatch({ type: 'UPDATE_FIELD', field: 'latitude', value: lat });
      dispatch({ type: 'UPDATE_FIELD', field: 'longitude', value: lng });
    } finally {
      dispatch({ type: 'SET_REVERSE', inProgress: false });
      setLoading(false);
    }
  }, []);

  // Initialize Leaflet map
  useEffect(() => {
    if (!mapContainerRef.current) return;

    // Import Leaflet dynamically to avoid SSR issues
    import('leaflet').then((L) => {

      // Create map with Neshan options (similar to your Laravel code)
      const mapOptions = {
        center: addressToEdit?.latitude && addressToEdit?.longitude ? [addressToEdit.latitude, addressToEdit.longitude] as [number, number] : [position[0], position[1]] as [number, number],
        zoom: 14,
        zoomControl: true,
      };

      const map = L.map(mapContainerRef.current!, mapOptions);
      mapRef.current = map;

      // Use OpenStreetMap tiles (most reliable for testing)
      const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        maxZoom: 19,
        attribution: '© OpenStreetMap contributors'
      });

      // Google Maps as backup
      const googleLayer = L.tileLayer('https://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
        maxZoom: 20,
        subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
        attribution: '© Google Maps'
      });

      // Add the tile layer to map with error handling
      osmLayer.addTo(map);

      // Add error handling for tile loading
      osmLayer.on('tileerror', () => {
        console.warn('OpenStreetMap tiles failed, switching to Google Maps');
        map.removeLayer(osmLayer);
        googleLayer.addTo(map);
      });

      console.log('Map tiles loaded successfully');

      // Add click event listener
      map.on('click', (e: any) => {
        const { lat, lng } = e.latlng;
        updateMarker(lat, lng);
        handleLocationSelected(lat, lng);
      });

      // Add initial marker
      if (addressToEdit?.latitude && addressToEdit?.longitude) {
        updateMarker(addressToEdit.latitude, addressToEdit.longitude);
      } else {
        updateMarker(position[0], position[1]);
      }
      //   updateMarker(position[0], position[1]);

      // Cleanup function
      return () => {
        map.remove();
      };
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run once on mount - functions are defined inside

  // Update marker function (based on your Laravel code)
  const updateMarker = useCallback((lat: number, lng: number) => {
    if (!mapRef.current) return;

    import('leaflet').then((L) => {
      if (!markerRef.current) {
        // Create new marker with custom icon (similar to your Laravel code)
        const marker = L.marker([lat, lng], {
          draggable: true,
          icon: L.divIcon({
            iconAnchor: [12, 24],
            className: 'custom-marker',
            html: `
              <div style="
                width: 24px;
                height: 24px;
                background-color: #ef4444;
                border: 2px solid white;
                border-radius: 50%;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                position: relative;
              ">
                <div style="
                  width: 8px;
                  height: 8px;
                  background-color: white;
                  border-radius: 50%;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "></div>
              </div>
            `,
          })
        });

        marker.addTo(mapRef.current);
        markerRef.current = marker;

        // Add drag event listener
        marker.on('dragend', () => {
          const { lat, lng } = marker.getLatLng();
          handleLocationSelected(lat, lng);
        });
      } else {
        // Update existing marker position
        markerRef.current.setLatLng([lat, lng]);
      }

      // Center map on marker
      mapRef.current.setView([lat, lng]);
    });
  }, [handleLocationSelected]);

  // Update marker when position changes
  useEffect(() => {
    if (position && mapRef.current) {
      updateMarker(position[0], position[1]);
    }
  }, [position, updateMarker]);

  /**
   * Handles form submission with validation
   */
  const handleSubmit = async () => {
    setHasSubmitted(true);

    // Validate form
    const validationErrors = validateForm();
    setErrors(validationErrors);

    // Check if there are any validation errors
    if (Object.keys(validationErrors).length > 0) {
      // Show toast with first error message
      const firstError = Object.values(validationErrors)[0];
      toast.error(`خطا در اعتبارسنجی: ${firstError}`);
      return;
    }

    setIsSubmitting(true);
    try {
      const payload = { ...formData, zip_code: String(formData.zip_code) };
      const response = addressToEdit
        ? await updateUserAddressAction(payload)
        : await createUserAddressAction(payload);
      debugger
      if (response.success) {
        toast.success(addressToEdit ? 'آدرس ویرایش شد!' : 'آدرس جدید ثبت شد!');
        onAddressCreated?.(response.data.id);
        // setSelectedAddress(response.data.id);
        // onClose();
      } else {
        toast.error(response.message || 'خطا در ثبت آدرس');
      }
    } catch (e) {
      console.error(e);
      toast.error('خطا در ارتباط با سرور');
    } finally {
      setIsSubmitting(false);
    }
  };

  // validate forms
  const getInputClasses = (fieldName: keyof ValidationErrors): string => {
    const baseClasses = "bg-[#F9FAFB] py-7 max-md:py-5 rounded-2xl transition-colors duration-200";
    const hasError = hasSubmitted && errors[fieldName];

    if (hasError) {
      return `${baseClasses} border-2 border-red-500 focus:border-red-500 focus:ring-red-500`;
    }

    return `${baseClasses} border-[#EFEFEF] focus:border-blue-500 focus:ring-blue-500`;
  };


  const renderErrorMessage = (fieldName: keyof ValidationErrors) => {
    if (hasSubmitted && errors[fieldName]) {
      return (
        <p className="text-red-500 text-sm mt-1 mr-1">
          {errors[fieldName]}
        </p>
      );
    }
    return null;
  };

  return (
    <>
      {/* <div className="flex flex-col h-full"> */}
      {loading && (
        <div className="absolute z-[1000] inset-0 bg-black bg-opacity-30 flex items-center justify-center">
          <div className="bg-white p-3 rounded-lg">
            <p>در حال دریافت اطلاعات آدرس...</p>
          </div>
        </div>
      )}
      {/* </div> */}
      {/* Form Section */}
      <div className="md:grid md:grid-cols-1 max-md:flex max-md:flex-col h-full">
        <div className="flex flex-col p-4 max-md:px-6">
          {/* Map Container */}
          <div className="w-full h-[230px] max-md:h-[200px] relative rounded-lg overflow-hidden border border-gray-200">
            {/* Map instructions */}
            <div className="absolute top-2 right-2 z-[1000] bg-white p-2 rounded-lg shadow-md text-xs max-w-[200px]">
              <p className="font-bold mb-1">راهنمای نقشه:</p>
              <p>• برای انتخاب مکان، روی نقشه کلیک کنید</p>
              <p>• نشانگر قرمز مکان انتخابی را نشان می‌دهد</p>
            </div>

            {/* Leaflet Map Container */}
            <div
              ref={mapContainerRef}
              className="w-full h-full"
              style={{ minHeight: '400px' }}
            />

            {isReverseGeocodingInProgress && (
              <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                <div className="bg-white p-3 rounded-lg">
                  <p>در حال دریافت اطلاعات آدرس...</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="max-md:flex  flex-col md:grid grid-cols-2 gap-4 px-6 py-4 bg-white">
          <div className="flex flex-col">
            <label className="text-base max-md:text-sm p-1 text-gray-500">نام آدرس</label>
            <input
              className={getInputClasses('name')}
              placeholder="مثال: خانه، محل کار"
              value={formData.name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('name', e.target.value)}
            />
            {renderErrorMessage('name')}
          </div>
          <div className="flex flex-col">
            <label className="text-base max-md:text-sm p-1 text-gray-500">استان</label>
            <input
              className={getInputClasses('province')}
              placeholder="استان خود را انتخاب کنید"
              value={formData.province}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('province', e.target.value)}
            />
            {renderErrorMessage('province')}
          </div>
           <div className="flex flex-col col-span-2">
            <label className="text-base max-md:text-sm p-1 text-gray-500">آدرس کامل</label>
            <input
              className={getInputClasses('address')}
              placeholder="مثال: کوچه شهید بهشتی، پلاک 41"
              value={formData.address}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('address', e.target.value)}
            />
            {renderErrorMessage('address')}
          </div>
          <div className="flex flex-col">
            <label className="text-base max-md:text-sm p-1 text-gray-500">شهر</label>
            <input
              className={getInputClasses('city')}
              placeholder="شهر خود را وارد کنید"
              value={formData.city}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('city', e.target.value)}
            />
            {renderErrorMessage('city')}
          </div>
          {/* <div>
            <Label className="text-base max-md:text-sm p-1 text-gray-500">کد پستی</Label>
            <Input
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              className={getInputClasses('zip_code')}
              placeholder="کد پستی ده رقمی"
              value={formData.zip_code}
              onChange={handleZipCodeChange}
              maxLength={10}
            />
            {renderErrorMessage('zip_code')}
          </div> */}
          {/* <div>
            <Label className="text-base max-md:text-sm p-1 text-gray-500">نام گیرنده</Label>
            <Input
              className={getInputClasses('receiver_name')}
              placeholder="نام گیرنده را وارد کنید"
              value={formData.receiver_name}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateField('receiver_name', e.target.value)}
            />
            {renderErrorMessage('receiver_name')}
          </div> */}
          {/* <div>
            <Label className="text-base max-md:text-sm p-1 text-gray-500">شماره تماس گیرنده</Label>
            <Input
              type="tel"
              inputMode="numeric"
              pattern="[0-9]*"
              className={getInputClasses('receiver_phone')}
              placeholder="09xxxxxxxxx"
              value={formData.receiver_phone}
              onChange={handlePhoneChange}
              maxLength={11}
            />
            {renderErrorMessage('receiver_phone')}
          </div> */}

         
          {/* <div className="flex flex-col col-span-2">
            <div className="flex items-center gap-3 mt-5 mr-1">
              <input
                type="checkbox"
                id="is_recipient_self"
                checked={formData.is_recipient_self}
                onChange={(e) => updateField('is_recipient_self', e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="is_recipient_self" className="text-sm text-gray-700">
                خودم گیرنده هستم
              </label>
            </div>
          </div> */}
        </div>

        {/* Submit Button - Positioned at bottom right of inputs */}
        <div className="mt-6 px-6 pb-4 w-1/2">
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || isReverseGeocodingInProgress}
            className="bg-primary py-6 text-white hover:bg-blue-400 rounded-xl w-full disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {isSubmitting ? (
              <span className="loader border-t-transparent border-4 border-white rounded-full w-4 h-4 animate-spin"></span>
            ) : null}
            {addressToEdit ? 'ذخیره تغییرات' : 'ثبت آدرس جدید'}
          </button>
        </div>
      </div>
    </>
  );
};

export default NeshanAddressForm;