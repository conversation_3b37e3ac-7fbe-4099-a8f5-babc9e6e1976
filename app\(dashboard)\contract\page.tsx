import { BookText, CloudUpload } from 'lucide-react'
import React from 'react'

const ContractPage = () => {
    return (
        <section className='bg-white p-5 px-10 max-md:px-3 border border-gray-300 rounded-2xl '>
            <div className='flex items-center gap-3'>
                <div className='bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3  rounded-b-4xl'>
                    <BookText />
                </div>
                <h1 className='md:text-lg text-base'>قرارداد و امضای هوشمد</h1>
            </div>
            <div className=' relative p-4 py-3  my-6 rounded-xl bg-orange-50 border border-yellow-400 border-dashed text-sm leading-6 text-gray-700'>
                <p className='qoute-hint'>
                    برای فعالیت در فروشگاه ما نیاز هست این قرار داد مطالعه و در صورت تائیدآن را قبول کنید
                </p>
            </div>

            <div className='px-1 max-h-[33rem] overflow-y-auto mt-10'>
                <p className='text-justify leading-8'>
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد وزمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.

                </p>
                <p className='text-justify leading-8 my-5'>
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد وزمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.

                </p>
                <p className='text-justify leading-8 my-5'>
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد وزمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.

                </p>
                <p className='text-justify leading-8 my-5'>
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. در این صورت می توان امید داشت که تمام و دشواری موجود در ارائه راهکارها و شرایط سخت تایپ به پایان رسد وزمان مورد نیاز شامل حروفچینی دستاوردهای اصلی و جوابگوی سوالات پیوسته اهل دنیای موجود طراحی اساسا مورد استفاده قرار گیرد.

                </p>
            </div>
            <div className='mt-8 flex max-md:flex-wrap max-md:flex-col-reverse gap-5'>
                <div className='w-[40%] max-md:w-full flex flex-col gap-3'>
                    <div className="flex items-center gap-2 text-base bg-muted p-5 rounded-xl border border-dashed border-gray-300">
                        <input
                            type="checkbox"
                            id="agreement"
                            className="w-4.5 h-4.5 accent-blue-600 cursor-pointer"
                        />
                        <label className='text-primary' htmlFor="agreement">من قرارداد را مطالعه کردم و قبول دارم</label>
                    </div>
                    <button className='w-full bg-primary text-white px-5 py-4 rounded-xl'>
                        دانلود قرارداد
                    </button>
                    <button className='w-full bg-warning text-white px-5 py-4 rounded-xl'>
                        قبول و ثبت
                    </button>

                </div>

                <div className="group relative w-[60%] max-md:w-full">
                    <input
                        type="file"
                        id="profilePhoto"
                        accept=".jpg,.jpeg,.png"
                        className="hidden"
                    // onChange={handleFileChange}
                    // disabled={isSubmitting}
                    />
                    <label
                        htmlFor="profilePhoto"
                        className={`flex flex-col justify-center items-center border-2 border-dashed border-gray-300 rounded-3xl p-3 cursor-pointer group-hover:border-primary transition-colors `} //${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
                    >
                        <div className="border-gray-300 rounded-full mb-4 group-hover:border-primary transition-colors">
                            <div className="w-20 h-20 bg-gray-400 flex items-center justify-center overflow-hidden rounded-full relative">
                                {/* {profileImageFile || formData.profile_image ? (
                                    <Image
                                        src={profileImageFile ? URL.createObjectURL(profileImageFile) : formData.profile_image || ""}
                                        alt="preview"
                                        fill
                                        className="w-full h-full object-cover"
                                    />
                                ) : ( */}
                                <CloudUpload stroke="white" size={30} />
                                {/* )} */}
                            </div>
                        </div>
                        <h3 className="text-center mb-2"> آپلود تصویر امضا </h3>
                        <p className="text-sm text-gray-500 mb-1 text-center leading-7"> در این مرحله لازم است با خودکار آبی یا مشکی روی یک صفحه سفید (بدون خط و تا خوردگی) امضا کرده و از آن عکس بگیرید. سپس عکس گرفته شده را بارگذاری کنید </p>
                        {/* <p className="text-sm">حداکثر حجم فایل: 3 مگابایت</p> */}
                    </label>
                </div>
            </div>

        </section>
    )
}

export default ContractPage