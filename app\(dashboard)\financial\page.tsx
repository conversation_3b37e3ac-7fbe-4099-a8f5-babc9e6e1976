import MoneyBagIcon from '@/components/common/svg/MoneyBagIcon'
import ProfitIcon from '@/components/common/svg/ProfitIcon'
import FinanceFilterWrapper from '@/components/Financial/FinanceFilterWrapper'
import { BanknoteArrowDown, FolderSync } from 'lucide-react'
import React from 'react'

const page = () => {
  return (
    <div>
        <section className='bg-white  max-md:px-3 border border-gray-200 rounded-t-2xl mt-5 '>
            <div className='md:p-5 max-md:py-3 md:px-10 md:flex justify-between items-center mb-5 max-md:grid max-md:grid-cols-2 max-md:gap-2'>
                <div className='bg-gray-50 md:p-3 max-md:py-3 md:px-6 max-md:px-1 md:w-[25%] h-full rounded-2xl flex gap-3 max-md:gap-2 max-md:text-sm  border-dashed border-gray-300 border-2'>
                    <div className='h-20 px-1.5 pt-2 bg-gradient-to-b from-gray-100 to-transparent rounded-t-full'>
                        <div className='bg-gradient-to-t from-[#35C65F] to-[#70ed93ba] text-white p-1.5 rounded-full'>
                            <MoneyBagIcon className='max-md:w-4 max-md:h-4' />
                        </div>
                    </div>
                    <div className='flex flex-col gap-3 mt-1'>
                        <h3>
                            موجودی قابل برداشت
                        </h3>
                        <div className='flex gap-1 items-center'>
                            <h1 className='text-[#35C65F] md:text-lg font-bold'>47.000.000</h1>
                            <span className='text-sm max-md:text-xs'>تومان</span>
                        </div>
                    </div>
                </div>
                <div className='bg-gray-50 md:p-3 max-md:py-3 md:px-6 max-md:px-1 md:w-[20%] h-full rounded-2xl flex gap-3  border-dashed border-gray-300 border-2 max-md:text-sm'>
                    <div className='h-20 px-1.5 pt-2 bg-gradient-to-b from-gray-100 to-transparent rounded-t-full'>
                        <div className='bg-gradient-to-t from-muted-foreground to-gray-200 text-white p-1.5 rounded-full'>
                            <ProfitIcon className='max-md:w-4 max-md:h-4' />
                        </div>
                    </div>
                    <div className='flex flex-col gap-3 mt-1'>
                        <h3>
                            کل تراکنش ها
                        </h3>
                        <div className='flex gap-1 items-center'>
                            <h1 className=' md:text-lg font-bold '>47</h1>
                            <span className='text-sm max-md:text-xs'>تراکنش</span>
                        </div>
                    </div>
                </div>
                <div className='bg-gray-50 md:p-3 max-md:py-3 md:px-6 max-md:px-1 md:w-[20%] h-full rounded-2xl flex gap-3  border-dashed border-gray-300 border-2 max-md:text-sm'>
                    <div className='h-20 px-1.5 pt-2 bg-gradient-to-b from-gray-100 to-transparent rounded-t-full'>
                        <div className='bg-gradient-to-t from-muted-foreground to-gray-200 text-white p-1.5 rounded-full'>
                            <FolderSync />
                        </div>
                    </div>
                    <div className='flex flex-col gap-3 mt-1'>
                        <h3>
                            درخواست ها
                        </h3>
                        <div className='flex gap-1 items-center'>
                            <h1 className=' md:text-lg font-bold '>4</h1>
                            <span className='text-sm max-md:text-xs'>مورد</span>
                        </div>
                    </div>
                </div>
                <div className='bg-gray-50 md:p-3 max-md:py-3 md:px-6 max-md:px-1 md:w-[20%] h-full rounded-2xl flex gap-3  border-dashed border-gray-300 border-2 max-md:text-sm'>
                    <div className='h-20 px-1.5 pt-2 bg-gradient-to-b from-gray-100 to-transparent rounded-t-full'>
                        <div className='bg-gradient-to-t from-muted-foreground to-gray-200 text-white p-1.5 rounded-full'>
                            <BanknoteArrowDown />
                        </div>
                    </div>
                    <div className='flex flex-col gap-3 mt-1'>
                        <h3>
                            برداشت ها
                        </h3>
                        <div className='flex gap-1 items-center'>
                            <h1 className=' md:text-lg font-bold '>8</h1>
                            <span className='text-sm max-md:text-xs'>برداشت</span>
                        </div>
                    </div>
                </div>
            </div>

        </section>
            <div className=''>
                <FinanceFilterWrapper />
            </div>
    </div>
  )
}

export default page