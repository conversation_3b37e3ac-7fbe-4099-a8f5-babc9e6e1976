import React from 'react';

interface ChatOutlineIconProps {
  size?: number;
  color?: string;
  className?: string;
  stroke?: string;
}

const ChatOutlineIcon: React.FC<ChatOutlineIconProps> = ({
  size = 24,
  color = '#fff',
  className = '',
  stroke = 'none',
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      aria-hidden="true"
      focusable="false"
      role="img"
      stroke={stroke}
    >
      <path
        fill={color}
        d="M6.75,7a.75.75,0,0,0,0,1.5Zm8,1.5a.75.75,0,0,0,0-1.5Zm-8,2.5a.75.75,0,0,0,0,1.5Zm4,1.5a.75.75,0,0,0,0-1.5Zm-1-11h2V0h-2ZM1.5,14.75v-5H0v5ZM11.75,18h-7v1.5h7ZM0,14.75A4.75,4.75,0,0,0,4.75,19.5V18A3.25,3.25,0,0,1,1.5,14.75Zm20-5A8.25,8.25,0,0,1,11.75,18v1.5A9.75,9.75,0,0,0,21.5,9.75ZM11.75,1.5A8.25,8.25,0,0,1,20,9.75h1.5A9.75,9.75,0,0,0,11.75,0ZM9.75,0A9.75,9.75,0,0,0,0,9.75H1.5A8.25,8.25,0,0,1,9.75,1.5Zm-3,8.5h8V7h-8Zm0,4h4V11h-4Z"
        transform="translate(1.25 2.25)"
      />
    </svg>
  );
};

export default ChatOutlineIcon;