import React from "react";

interface ProfitIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
  color?: string;
}

const ProfitIcon: React.FC<ProfitIconProps> = ({
  size = 24,
  color = "currentColor",
  className,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      {...props}
    >
      <path
        d="M16.864,12.031a.75.75,0,1,0-1.39-.563ZM.636,5.469a.75.75,0,0,0,1.39.563ZM17.5,8.75A8.75,8.75,0,0,0,8.75,0V1.5A7.25,7.25,0,0,1,16,8.75ZM0,8.75A8.75,8.75,0,0,0,8.75,17.5V16A7.25,7.25,0,0,1,1.5,8.75ZM8.75,17.5a8.753,8.753,0,0,0,8.114-5.469l-1.39-.563A7.253,7.253,0,0,1,8.75,16ZM8.75,0A8.753,8.753,0,0,0,.636,5.469l1.39.563A7.253,7.253,0,0,1,8.75,1.5Z"
        transform="translate(3.25 3.25)"
        fill={color}
      />
      <path
        d="M5.5,2.75a.75.75,0,0,1-1.5,0ZM2.75,4a.75.75,0,0,1,0,1.5ZM4,2.75A1.25,1.25,0,0,0,2.75,1.5V0A2.75,2.75,0,0,1,5.5,2.75ZM2.75,1.5A1.25,1.25,0,0,0,1.5,2.75H0A2.75,2.75,0,0,1,2.75,0ZM1.5,2.75A1.25,1.25,0,0,0,2.75,4V5.5A2.75,2.75,0,0,1,0,2.75Z"
        transform="translate(9.25 7.25)"
        fill={color}
      />
      <path
        d="M2.75,1.5a.75.75,0,0,1,0-1.5ZM0,2.75a.75.75,0,0,1,1.5,0Zm4,0A1.25,1.25,0,0,0,2.75,1.5V0A2.75,2.75,0,0,1,5.5,2.75Zm-2.5,0A1.25,1.25,0,0,0,2.75,4V5.5A2.75,2.75,0,0,1,0,2.75ZM2.75,4A1.25,1.25,0,0,0,4,2.75H5.5A2.75,2.75,0,0,1,2.75,5.5Z"
        transform="translate(9.25 11.25)"
        fill={color}
      />
      <path
        d="M1.5.75A.75.75,0,0,0,0,.75ZM0,2.25a.75.75,0,0,0,1.5,0ZM0,.75v1.5H1.5V.75Z"
        transform="translate(11.25 5.75)"
        fill={color}
      />
      <path
        d="M1.5.75A.75.75,0,0,0,0,.75ZM0,2.25a.75.75,0,0,0,1.5,0ZM0,.75v1.5H1.5V.75Z"
        transform="translate(11.25 15.25)"
        fill={color}
      />
      <path
        d="M1.086.579A.75.75,0,0,0,.415,1.921ZM5.2,1.35A.75.75,0,1,0,4.3.15ZM.415,1.921l.894.447L1.98,1.026,1.086.579Zm3.774.188L5.2,1.35,4.3.15,3.289.908Zm-2.88.26a2.75,2.75,0,0,0,2.88-.26l-.9-1.2a1.25,1.25,0,0,1-1.309.118Z"
        transform="translate(17.25 9.75)"
        fill={color}
      />
      <path
        d="M1.086,2.079A.75.75,0,0,1,.415.738ZM5.2,1.308a.75.75,0,0,1-.9,1.2ZM.415.738,1.309.29,1.98,1.632l-.894.447ZM4.189.55,5.2,1.308l-.9,1.2L3.289,1.75ZM1.309.29a2.75,2.75,0,0,1,2.88.26l-.9,1.2A1.25,1.25,0,0,0,1.98,1.632Z"
        transform="translate(1.25 11.592)"
        fill={color}
      />
    </svg>
  );
};

export default ProfitIcon;
