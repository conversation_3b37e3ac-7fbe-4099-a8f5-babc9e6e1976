import { Rotate<PERSON><PERSON><PERSON>, <PERSON><PERSON>heckBig, ShoppingCart } from 'lucide-react'
import React from 'react'

const OrderItem = () => {
  return (
    <div className="even:bg-gray-50 flex items-center md:justify-between md:gap-5 max-md:gap-2 border border-gray-300 p-5 px-5 max-md:px-2 rounded-2xl">
            <div className="p-2 w-32 max-md:px-3 max-md:w-[40%] border-2 border-dashed border-gray-300 rounded-2xl flex justify-center items-center flex-col gap-3">
              <div className="bg-primary text-white p-1 max-md:p-1.5 rounded-full">
                <RotateCcw size={25} className="border rounded-full p-1" />
              </div>
              <span className="text-sm text-primary">منتظر تایید</span>
            </div>
            <div className="w-full flex flex-col gap-3">
              <div className="flex max-md:flex-wrap items-center max-md:gap-3 gap-5 max-md:text-sm">
                <p>
                  <span className="font-light">کد پیگیری: </span>
                  <span className="font-bold">2316311</span>
                </p>
                <p>
                  <span className="font-light">تاریخ سفارش: </span>
                  <span className="font-light md:text-lg">10 آذر 1403</span>
                </p>
                <p>
                  <span className="font-light">تعداد: </span>
                  <span className="font-bold text-green-600 text-lg mx-1">
                    5
                  </span>
                  <span>کالا</span>
                </p>
                <p className="flex items-center gap-1">
                  <span className="font-light">مبلغ کل: </span>
                  <span className="font-bold text-red-500">89990000</span>
                  <span className="font-light">تومان</span>
                  <CircleCheckBig className="w-4 text-green-600" />
                </p>
              </div>
              <div className="flex items-center gap-5">
                <p>
                  <span className="font-light">سفارش از: </span>
                  <span className="font-bold"> مائده صادقی </span>
                </p>
                <p>
                  <span className="font-light">استان ارسالی: </span>
                  <span className="font-bold"> مشهد </span>
                </p>
              </div>
            </div>
            <div className='max-md:hidden'>
              <button className="bg-[#363A3E] text-white flex items-center p-2 max-md:p-1.5 rounded-lg gap-1">
                <ShoppingCart size={22} /> مدیریت
              </button>
            </div>
          </div>
  )
}

export default OrderItem