import { Pagination } from "./common.types";

export interface Ticket {
  id: number;
  title: string;
  ticket_status: 'pending' | 'answered' | 'closed' | string;
  created_at: string; 
  created_date_ago: string;
}

export interface TicketsResponse {
    tickets: Ticket[];
    pagination: Pagination;
}

export interface TicketMessage {
  id: number;
  attachment: null | string;
  message: string;
  is_admin: boolean;
  user_name?: string;
  created_at: string;
}

export interface TicketMessagesResponse {
    tickets: TicketMessage[];
    pagination: Pagination;
}
