import React from "react";

interface TelegramIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number; // Optional size for both width & height
  color?: string; // Optional fill color
}

const TelegramIcon: React.FC<TelegramIconProps> = ({
  size = 16,
  color = "#6f7580",
  className,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={(size * 14.217) / 16.182} // Keep aspect ratio
      viewBox="0 0 16.182 14.217"
      className={className}
      {...props}
    >
      <g
        id="Huge-icon_social_solid_telegram"
        data-name="Huge-icon/social/solid/telegram"
        transform="translate(-2.293 -4.062)"
      >
        <g id="telegram" transform="translate(2.293 4.062)">
          <path
            id="Subtract"
            d="M.562,6.267,14.882.078a.931.931,0,0,1,1.288,1L14.212,13.432a.93.93,0,0,1-1.6.493L9.4,10.521A1.861,1.861,0,0,1,9.287,8.1l2.269-2.92a.186.186,0,0,0-.251-.269L6.794,7.939a3.722,3.722,0,0,1-2.606.593L.8,8.042A.931.931,0,0,1,.562,6.267Z"
            transform="translate(0 0)"
            fill={color}
          />
        </g>
      </g>
    </svg>
  );
};

export default TelegramIcon;
