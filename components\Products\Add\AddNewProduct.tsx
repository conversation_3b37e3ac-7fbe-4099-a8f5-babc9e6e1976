"use client";
import DropdownSearch, { Category } from "@/components/common/DropdownSearch";
import { FilePlus } from "lucide-react";
import { useRouter } from "nextjs-toploader/app";
import React, { useState } from "react";
import toast, { Toaster } from "react-hot-toast";
interface DropdownItem {
  id: string | number;
  label: string;
  //   [key: string]: any;
}

const sampleCategories: Category[] = [
  {
    id: 1,
    label: "خودرو",
    children: [
      {
        id: 11,
        label: "سواری",
        children: [
          { id: 111, label: "پژو" },
          { id: 112, label: "سمند" },
          { id: 113, label: "پراید" },
          { id: 114, label: "دنا" },
        ],
      },
      {
        id: 12,
        label: "وانت",
        children: [
          { id: 121, label: "پیکان وانت" },
          { id: 122, label: "زامیاد" },
        ],
      },
      {
        id: 13,
        label: "موتورسیکلت",
        children: [
          { id: 131, label: "هوندا" },
          { id: 132, label: "یاماها" },
          { id: 133, label: "باجاج" },
        ],
      },
    ],
  },
  {
    id: 2,
    label: "املاک",
    children: [
      {
        id: 21,
        label: "فروش مسکونی",
        children: [
          { id: 211, label: "آپارتمان" },
          { id: 212, label: "خانه ویلایی" },
          { id: 213, label: "زمین مسکونی" },
        ],
      },
      {
        id: 22,
        label: "اجاره مسکونی",
        children: [
          { id: 221, label: "آپارتمان" },
          { id: 222, label: "خانه ویلایی" },
          { id: 223, label: "سوئیت و استودیو" },
        ],
      },
      {
        id: 23,
        label: "فروش اداری و تجاری",
        children: [
          { id: 231, label: "دفتر کار" },
          { id: 232, label: "مغازه" },
          { id: 233, label: "صنعتی و کشاورزی" },
        ],
      },
    ],
  },
  {
    id: 3,
    label: "کالای دیجیتال",
    children: [
      {
        id: 31,
        label: "موبایل",
        children: [
          { id: 311, label: "سامسونگ" },
          { id: 312, label: "اپل" },
          { id: 313, label: "شیائومی" },
          { id: 314, label: "هوآوی" },
        ],
      },
      {
        id: 32,
        label: "تبلت",
        children: [
          { id: 321, label: "آیپد" },
          { id: 322, label: "تبلت سامسونگ" },
          { id: 323, label: "تبلت لنوو" },
        ],
      },
      {
        id: 33,
        label: "لپ‌تاپ",
        children: [
          { id: 331, label: "ایسوس" },
          { id: 332, label: "اچ پی" },
          { id: 333, label: "لنوو" },
          { id: 334, label: "مک‌بوک" },
        ],
      },
    ],
  },
  {
    id: 4,
    label: "خدمات",
    children: [
      { id: 41, label: "آموزش" },
      { id: 42, label: "طراحی سایت" },
      { id: 43, label: "تعمیرات" },
      { id: 44, label: "حمل و نقل" },
    ],
  },
];
const AddNewProduct = () => {
  const [selectedItem, setSelectedItem] = useState<DropdownItem | null>(null);
  const router = useRouter();
  const items = [
    { id: 1, label: " خودرو" },
    { id: 2, label: "لپتاپ" },
    { id: 3, label: "آیتم ۳" },
    // ...
  ];
  const handleNextStep = () => {
    debugger;
    if (selectedItem) {
      router.push("/add-product?step=2");
    } else {
      toast.error("لطفا یک آیتم را انتخاب کنید", {
        position: "top-right",
        duration: 4000,
      });
    }
  };
  return (
    <div className="bg-white p-3 px-5 rounded-xl shadow-md overflow-hidden flex flex-col gap-5">
      <div className="add-product-header flex items-center gap-3">
        <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3  rounded-b-4xl">
          <FilePlus />
        </div>
        <div className="w-full flex items-center gap-1">
          <h2 className="">افزودن محصول جدید</h2>
          <div className=" title-line h-px bg-gray-300 w-[70%]"></div>
        </div>
      </div>
      <div className="p-4  my-6 rounded-xl bg-orange-50 border border-yellow-400 border-dashed space-y-4 text-sm leading-6 text-gray-700">
        <h4>افزودن محصول اختصاصی</h4>
        <p>
          از اینجا میتوانید محصول خود را که در سایت خودروکس نیست را اضافه کنید و
          بعد از برسی و تائید در سایت منتشر کنید
        </p>
      </div>
      <div className="mt-5">
        <DropdownSearch
          items={items}
          placeholder="یک آیتم انتخاب کنید"
          // searchPlaceholder="جستجو..."
          onSelect={setSelectedItem}
          selectedItem={selectedItem}
          className="w-64"
          useModal={true}
          categories={sampleCategories}
        />
      </div>
      <div className="mt-5">
        <button
          onClick={handleNextStep}
          className="bg-primary text-white px-5 py-3.5 rounded-2xl"
        >
          شروع و ایجاد محصول جدید
        </button>
      </div>
    
    </div>
  );
};

export default AddNewProduct;
