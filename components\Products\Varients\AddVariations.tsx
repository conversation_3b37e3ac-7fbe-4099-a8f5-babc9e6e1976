"use client";
import { ChevronDown, FilePlus } from "lucide-react";
import { useRouter } from "nextjs-toploader/app";
import { useState } from "react";

const AddVariations = () => {
  const router = useRouter();
  const [attributes, setAttributes] = useState([
    { color: "", size: "", price: "", quantity: "" },
  ]);

  const titleOptions: string[] = ["رنگ", "سایز", "وزن"]; // می‌تونی این رو از API هم بیاری
  //   const valueOptions: { [key: string]: string[] } = {
  //     رنگ: ["قرمز", "آبی", "سبز", "مشکی"],
  //     سایز: ["S", "M", "L", "XL"],
  //     وزن: ["500 گرم", "1000 گرم", "1500 گرم"],
  //   };
  const colors = ["قرمز", "آبی", "سبز", "مشکی"];
  const sizes = ["S", "M", "L", "XL"];

  const handleAddAttribute = () => {
    setAttributes([
      ...attributes,
      { color: "", size: "", price: "", quantity: "" },
    ]);
  };

  const handleChange = (
    index: number,
    field: "color" | "size" | "price" | "quantity",
    newValue: string
  ) => {
    const updated = [...attributes];
    updated[index][field] = newValue;

    // if (field === "title") updated[index].value = "";

    setAttributes(updated);
  };

  return (
    <div className="flex flex-col gap-7">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3 rounded-b-4xl">
            <FilePlus />
          </div>
          <div className="flex items-center gap-1">
            <h2>تنوع های محصول</h2>
          </div>
        </div>
        <button
          type="button"
          onClick={handleAddAttribute}
          className="bg-muted-foreground text-white p-2 px-3 rounded-lg !cursor-pointer"
        >
          افزودن ویژگی
        </button>
      </div>
      <div className="flex flex-col gap-5 max-md:divide-y max-md:divide-gray-300 ">
        {attributes.map((attr, index) => (
          <div
            key={index}
            className="flex md:gap-5 max-md:gap-y-7 max-md:pb-10
             
              max-md:flex-wrap justify-between"
          >
            {" "}
            <div className="flex flex-col gap-3 w-full max-md:w-[48%] ">
              <label className="px-1"> رنگ </label>
              <div className="relative w-full">
                <select
                  value={attr.color}
                  onChange={(e) => handleChange(index, "color", e.target.value)}
                  className="appearance-none w-full border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
                >
                  <option value="">انتخاب کنید</option>
                  {colors.map((color) => (
                    <option key={color} value={color}>
                      {color}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-3 w-full max-md:w-[48%]">
              <label className="px-1"> سایز </label>
              <div className="relative">
                <select
                  value={attr.size}
                  onChange={(e) => handleChange(index, "size", e.target.value)}
                  className="appearance-none border w-full bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
                  //   disabled={!attr.title}
                >
                  <option value="">انتخاب کنید</option>
                  {sizes.map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-3 w-full max-md:w-[48%]">
              <label className="px-1">قیمت</label>
              <input
                type="number"
                value={attr.price}
                onChange={(e) => handleChange(index, "price", e.target.value)}
                placeholder="مثال: 120000"
                className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
              />
            </div>
            <div className="flex flex-col gap-3 w-full max-md:w-[48%]">
              <label className="px-1">موجودی انبار</label>
              <input
                type="number"
                value={attr.quantity}
                onChange={(e) => handleChange(index, "quantity", e.target.value)}
                placeholder="مثال: 10"
                className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
              />
            </div>
          </div>
        ))}
        
      </div>
      <div className="flex justify-end w-full">
        <button
          onClick={() => router.push("/add-product?step=4")}
          className="bg-primary cursor-pointer text-white px-5 py-3.5 rounded-xl"
        >
          ثبت و ادامه
        </button>
      </div>
    </div>
  );
};

export default AddVariations;
