import type { Metadata } from "next";

import "../globals.css";
import Sidebar from "@/components/common/Sidebar";
import DashboardNavbar from "@/components/Header/DashboardNavbar";


export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};



export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
        <section className="container mx-auto">
          <div className='flex items-start gap-8 md:p-3 mb-10 '>
            <div className='hidden lg:block h-full'>
              <Sidebar />
            </div>

            <div className='w-full flex flex-col gap-6 md:p-3'>
              <DashboardNavbar />
              {children}
              
            </div>
          </div>
        </section>
  );
}
