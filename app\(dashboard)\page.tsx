import ChatAcceptIcon from "@/components/common/svg/ChatIcons/ChatAcceptIcon";
import ChatIcon from "@/components/common/svg/ChatIcons/ChatIcon";
import ChatLockIcon from "@/components/common/svg/ChatIcons/ChatLockIcon";
import ChatOutlineIcon from "@/components/common/svg/ChatIcons/ChatOutlineIcon";
import ChatSearchIcon from "@/components/common/svg/ChatIcons/ChatSearchIcon";
import OrderItem from "@/components/Main/OrderList/OrderItem";
import AddNewProductContainer from "@/components/Products/Add/AddNewProductContainer";
import ProductsListItem from "@/components/ProductsList/ProductsListItem";
import {
  ChevronsLeft,
  CircleCheckBig,
  CircleChevronLeft,
  CircleDollarSign,
  FilePlus,
  MessageCircleMore,
  PencilLine,
  RotateCcw,
  ShoppingBag,
  ShoppingCart,
  Star,
  Ticket,
  Zap,
} from "lucide-react";

export default function Home() {
  return (
    <div className="max-md:px-2">
      <div className="md:grid grid-cols-12 max-md:flex max-md:flex-col gap-8">
        <section className="bg-white p-3 px-5 max-md:px-3 rounded-2xl col-span-8 shadow-sm order-2">
          <div className="flex items-center justify-between">
            <div className="flex gap-3 items-center">
              <div className="bg-[#FFD141] p-2 rounded-full w-fit">
                <MessageCircleMore color="white" />
              </div>
              <h3>وضعیت تیکت ها</h3>
            </div>
            <div className="flex gap-2 items-center">
              <span>مشاهده همه</span>
              <span>
                <ChevronsLeft size={18} />
              </span>
            </div>
          </div>
          <div className="grid grid-cols-4 max-md:grid-cols-2 gap-3 max-md:gap-2 mt-5">
            <div className="col-span-1 p-3 max-md:px-1.5 py-3 md:pb-4 md:pr-6 border-2 border-dashed border-gray-300 rounded-xl">
              <div className="flex flex-col gap-2 w-fit max-md:w-full">
                <div className="bg-[#5CE183] p-2 w-12 max-md:w-10 rounded-full">
                  <ChatOutlineIcon className="w-full h-full" />
                </div>
                <div className="font-bold px-2">
                  <h3 className="text-[#5CE183] text-2xl max-md:text-lg">0</h3>
                  <h4 className="font-light md:text-lg text-sm">باز</h4>
                </div>
              </div>
            </div>
            <div className="col-span-1 p-3 max-md:px-1.5 py-3 pb-4 md:pr-6 border-2 border-dashed border-gray-300 rounded-xl">
              <div className="flex flex-col gap-2  w-fit">
                <div className="bg-primary p-2 w-12 max-md:w-10 rounded-full">
                  <ChatSearchIcon className="w-full h-full" />
                </div>
                <div className="font-bold px-2">
                  <h3 className="text-primary text-2xl max-md:text-lg">0</h3>
                  <h4 className="font-light md:text-lg text-sm">در حال بررسی</h4>
                </div>
              </div>
            </div>
            <div className="col-span-1 p-3 max-md:px-1.5 py-3 pb-4 md:pr-6 border-2 border-dashed border-gray-300 rounded-xl">
              <div className="flex flex-col gap-2  w-fit">
                <div className="bg-[#A887C0] p-2 w-12 max-md:w-10 rounded-full">
                  <ChatAcceptIcon className="w-full h-full" />
                </div>
                <div className="font-bold px-2">
                  <h3 className="text-[#A887C0] text-2xl max-md:text-lg">3</h3>
                  <h4 className="font-light md:text-lg text-sm">پاسخ داده شده</h4>
                </div>
              </div>
            </div>
            <div className="col-span-1 p-3 max-md:px-1.5 py-3 pb-4 md:pr-6 border-2 border-dashed border-gray-300 rounded-xl">
              <div className="flex flex-col gap-2  w-fit">
                <div className="bg-[#FD6B48] p-2 w-12 max-md:w-10 rounded-full">
                  <ChatLockIcon className="w-full h-full" />
                </div>
                <div className="font-bold px-2">
                  <h3 className="text-[#FD6B48] text-2xl max-md:text-lg">3</h3>
                  <h4 className="font-light md:text-lg text-sm">بسته شده</h4>
                </div>
              </div>
            </div>
          </div>
        </section>
        <section className="bg-white p-3 px-5 rounded-2xl col-span-4 shadow-sm cart-circles-blue flex flex-col py-8 max-md:order-1">
          <div className="flex items-center gap-2">
            <div className="bg-gray-200 rounded-full p-1 w-16 h-16"></div>
            <div className="flex flex-col gap-2">
              <p className="font-bold text-xl">سپهر پلاس</p>
              <div className=" flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Star color="#F7BC06" fill="#F7BC06" size={20} />
                  <span>3 از 5</span>
                  <span>(408)</span>
                </div>
              </div>
            </div>
          </div>
          <div className="p-4 py-2  my-3 mt-5 rounded-xl bg-orange-50 border border-yellow-400 border-dashed text-sm leading-6 text-gray-700">
            <p>
              <span className="text-warning">67%</span>
              <span className="font-light mx-1">رضایت از خرید</span>
              <span className="mx-2"> | </span>
              <span className="font-light">
                از <span className="text-green-400 font-bold"> 48 </span> رای
              </span>
            </p>
          </div>
          <div className="flex items-center justify-between">
            <button className="bg-muted flex items-center gap-2 border border-gray-200 rounded-xl py-2 px-5 w-[40%] text-right">
              <PencilLine size={18} />
              ویرایش
            </button>
            <button className="bg-muted flex items-center justify-between border border-gray-200 rounded-xl py-2 px-5 w-[55%] text-right">
              مشاهده فروشگاه
              <CircleChevronLeft size={20} />
            </button>
          </div>
        </section>
      </div>

      <section className="bg-white p-3 px-5 rounded-2xl col-span-8 shadow-sm mt-5">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3  rounded-b-4xl">
              <ShoppingCart />
            </div>
            <h1 className="md:text-lg text-base"> عملکرد فروش </h1>
          </div>
          <div className="flex gap-2 items-center">
            <span>مشاهده همه</span>
            <span>
              <ChevronsLeft size={18} />
            </span>
          </div>
        </div>
        <div className="md:grid grid-cols-12 gap-8 mt-5">
          <div className="col-span-7 border rounded-2xl border-gray-300 max-md:hidden"></div>
          <div className="col-span-5 grid grid-cols-2 gap-3">
            <div className="p-3 border rounded-2xl border-gray-300">
              <div className=" flex items-center justify-between">
                <p className="text-sm">فروش امروز</p>
                <div className="bg-primary text-white rounded-full p-1.5">
                  <CircleDollarSign
                    className="bg-primary text-white rounded-full"
                    size={22}
                  />
                </div>
              </div>
              <p className="mt-3 font-light">
                <span className="text-primary font-bold mx-1 text-lg">
                  8400000
                </span>
                تومان
              </p>
            </div>
            <div className="p-3 border rounded-2xl border-gray-300">
              <div className=" flex items-center justify-between">
                <p className="text-sm"> تعداد فروش امروز</p>
                <div className="bg-warning text-white rounded-full p-1.5">
                  <ShoppingBag className="" size={22} />
                </div>
              </div>
              <p className="mt-3 font-light">
                <span className="text-warning font-bold mx-1 text-lg">12</span>
                کالا
              </p>
            </div>
            <div className="p-3 border rounded-2xl border-gray-300 col-span-2">
              <div className="flex items-center justify-between flex-wrap">
                <div className="font-bold py-5 w-1/2 px-3 flex flex-col gap-2">
                  <p> درآمد کل </p>
                  <p className="">
                    <span>7660000</span>
                    <span className="font-light mr-1">تومان</span>
                  </p>
                </div>
                <div className="py-5 w-1/2 px-3 border-r-gray-300 border-r flex flex-col items-center text-right">
                  <div className="flex flex-col gap-2 font-bold">
                    <p> فروش کالا </p>
                    <p>
                      <span>452</span>
                      <span className="font-light mr-1">عدد</span>
                    </p>
                  </div>
                </div>
                <div className="w-full border-2 border-dashed border-gray-300 rounded-2xl mt-5 p-3 flex justify-between items-center">
                  <div>
                    <h4 className="mb-3">موجودی قایل تسویه</h4>
                    <p>
                      <span className="font-bold text-lg text-[#00A76F]">
                        4700000
                      </span>
                      <span className="font-light"> تومان </span>
                    </p>
                  </div>
                  <div>
                    <button className="bg-[#00A76F] text-white px-4 py-2 rounded-xl mt-3">
                      درخواست تسویه
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="bg-white p-3 px-5 max-md:px-3 rounded-2xl shadow-sm mt-5">
        <div className="add-product-header flex items-center gap-3 w-full mb-8">
          <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3 rounded-b-4xl">
            <FilePlus />
          </div>

          <div className="flex items-center gap-1">
            <h2>سفارشات دریافتی</h2>
          </div>

          <div className="flex-1 h-px bg-gray-300 mx-2" />

          <div className="flex items-center gap-1">
            <h2 className="text-sm flex items-center gap-1">
              مشاهده همه <ChevronsLeft size={18} />{" "}
            </h2>
          </div>
        </div>
        <div className="flex flex-col gap-5 ">
          <OrderItem />
          <OrderItem />
        </div>
      </section>

      <section className="mt-10">
        <div className="add-product-header flex items-center gap-3 w-full mb-8">
          <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent  rounded-b-4xl">
            <Ticket />
          </div>

          <div className="flex items-center gap-1">
            <h2>
              محصولات من
            </h2>
          </div>

          <div className="flex-1 h-px bg-gray-300 mx-2" />

          <div className="flex items-center gap-1">
            <h2 className="text-sm flex items-center gap-1">
              مشاهده همه <ChevronsLeft size={18} />{" "}
            </h2>
          </div>
        </div>
        <div className="flex justify-between max-md:flex-col max-md:gap-5">
          <div className="md:w-[32%]">
          <ProductsListItem />

          </div>
          <div className="md:w-[32%]">
          <ProductsListItem />

          </div>
          <div className="md:w-[32%]">
          <ProductsListItem />

          </div>
          
        </div>
      </section>
    </div>
  );
}
