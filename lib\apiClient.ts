"use server"

import { cookies } from "next/headers";

interface ApiClientOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE";
  headers?: Record<string, string>;
  body?: any;
  credentials?: RequestCredentials;
  next?: {
    tags?: string[];
    revalidate?: number;
  };
  base?: "default" | "alt";
}

const BASE_URL = process.env.BASE_URL as string;
const APPLICATION_TOKEN = process.env.X_APPLICATION_TOKEN as string;
const BASE_URL_2 = process.env.BASE_URL_2 as string;


export async function apiClient(endpoint: string, options: ApiClientOptions = {}, isForm: boolean = false) {
  const { method, headers = {}, body, credentials = "include", next, base = "default" } = options;
  const selectedBaseUrl = base === "alt" ? BASE_URL_2 : BASE_URL;
  // const X_APPLICATION_TOKEN = base !== "alt" ? APPLICATION_TOKEN : "matin_token";

  const defaultHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    "Accept": "application/json",
    "X-Application-Token": APPLICATION_TOKEN,
  };

  if (isForm && "Content-Type" in defaultHeaders) {
    delete defaultHeaders["Content-Type"];
  }




  const finalHeaders: Record<string, string> = { ...defaultHeaders, ...headers };


  const cookieStore = await cookies();
  const accessToken = cookieStore.get('authorization')?.value;
  if (accessToken) {
    finalHeaders.Authorization = `Bearer ${accessToken}`;
  }
  // console.log(finalHeaders);
  // console.log(`${selectedBaseUrl}${endpoint}`);
  // console.log("-------------------", finalHeaders);


  try {
    

    return await fetch(`${selectedBaseUrl}${endpoint}`, {
      method,
      headers: finalHeaders,
      credentials,
      body: body ? isForm ? body : JSON.stringify(body) : undefined,
      next,
    });


    // if (!response.ok) {
    //   const errorData = await response.json();
    //   throw new Error(errorData.message || "خطایی رخ داده است");
    // }

    // return await response.json();
  } catch (error) {
    console.error("API call error:", error);
    throw error;
  }
}