@import "tailwindcss";
@config "../tailwind.config.ts";


@layer base {

  :root {
    --background: #f5f6f8;
    --foreground: #62676E;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: #1F84FB;
    --primary-foreground: #0A0A0C;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: #F9FAFB;
    --muted-foreground: #9DA5B0;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: #15192A;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: #E4E6E9;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }


  body {
    background-color: var(--background);
    color: var(--foreground);
  }

  input::-webkit-inner-spin-button,
  input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }

}

/* Tiptap Editor Styles */
.ProseMirror {
  outline: none;
}

.ProseMirror h1 {
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 2.25rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 2rem;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

.ProseMirror p {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror strong {
  font-weight: 700;
}

.ProseMirror em {
  font-style: italic;
}

.ProseMirror ul {
  list-style-type: disc;
  margin-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror ol {
  list-style-type: decimal;
  margin-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror li {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin-left: 0;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  font-style: italic;
  color: #6b7280;
}

.ProseMirror pre {
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  overflow-x: auto;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.875rem;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 1rem 0;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}



.title-border-bottom::before {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 120%;
  height: 2px;
  background-color: #e5e7eb;
}

.title-border-bottom::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 55%;
  height: 2px;
  background-color: #F7BC06;
}

.qoute-hint {
  background-image: url('/assets/images/double-comma.png');
  background-repeat: no-repeat;
  background-size: 45px auto;
  background-position: 10px 0px;
  min-height: 30px;
}

.cart-circles-yellow {
  background-image: url("/assets/images/half-circle-yellow.png");
  background-repeat: no-repeat;
}

.cart-circles-blue {
  background-image: url("/assets/images/cart-circles.webp");
  background-repeat: no-repeat;
  background-size: 250px;
}

.seller-stats div {
  width: 100%;
}

.card-details h3 {
  position: relative;
}

.card-details h3::before {
  position: absolute;
  content: '';
  background-color: #1F84FB;
  height: 2px;
  right: 0;
  bottom: 0;
  z-index: 2;
  width: 80px;
}

.card-details h3::after {
  position: absolute;
  content: '';
  background-color: #ebebeb;
  height: 2px;
  right: 0;
  bottom: 0;
  width: 200px;
}

/* #_rht_toaster{
  direction: ltr !important;
} */