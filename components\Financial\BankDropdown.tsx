"use client";

import { useState } from "react";
import Image, { StaticImageData } from "next/image";
import { ChevronDown } from "lucide-react";
import MeliBank from "@/public/assets/images/5.png"
import MelatBank from "@/public/assets/images/Melaat_bank.png"
type Bank = {
  id: string;
  name: string;
  card: string;
  logo: StaticImageData;
};

const banks: Bank[] = [
  {
    id: "melli",
    name: "بانک ملی",
    card: "6037 9975 1234 5678",
    logo: MelatBank,
  },
  {
    id: "mellat",
    name: "بانک ملت",
    card: "6104 3377 1234 5678",
    logo: MeliBank,
  },
  {
    id: "tejarat",
    name: "بانک تجارت",
    card: "5859 8370 1234 5678",
    logo: MelatBank,
  },
];

export default function BankDropdown() {
  const [selected, setSelected] = useState<Bank | null>(null);
  const [open, setOpen] = useState(false);

  return (
    <div className="w-full max-w-md" >
      <div
        onClick={() => setOpen(!open)}
        className="relative flex items-center gap-3 h-16 w-full border rounded-2xl px-4 py-3 cursor-pointer bg-white shadow-sm"
      >
         {/* Right - Logo */}
        {selected && (
          <Image
            src={selected.logo}
            alt={selected.name}
            width={32}
            height={32}
            className="object-contain"
          />
        )}
       
        <span className="text-gray-500 text-sm">
          {selected ? selected.card : "انتخاب بانک"}
        </span>

       
         
        <ChevronDown
          size={20}
          className={`text-gray-400 transition-transform absolute left-5 ${
            open ? "rotate-180" : ""
          }`}
        />
      </div>
      

      {/* Dropdown list */}
      {open && (
        <div className="mt-2 border rounded-2xl bg-white shadow-lg overflow-hidden">
          {banks.map((bank) => (
            <div
              key={bank.id}
              onClick={() => {
                setSelected(bank);
                setOpen(false);
              }}
              className="flex items-center gap-3 px-4 py-3 hover:bg-gray-100 cursor-pointer"
            >
              {/* Logo */}
              <Image
                src={bank.logo}
                alt={bank.name}
                width={32}
                height={32}
                className="object-contain"
              />
              {/* Card number */}
              <span className="text-gray-700 text-sm">{bank.card}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
