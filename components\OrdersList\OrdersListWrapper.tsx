'use client';

import { <PERSON><PERSON><PERSON>ck<PERSON>ig, Eye, List<PERSON>heck, Search, XCircle, Loader2, FilterIcon, RotateCcw, ShoppingCart } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import UndoCircleIcon from "../common/svg/UndoCircleIcon";
import PackageBoxIcon from "../common/svg/PackageBoxIcon";

// import FilterIcon from "@/components/common/svg/FilterIcon";
// import PackageBoxIcon from "@/components/common/svg/PackageBoxIcon";
// import UndoCircleIcon from "@/components/common/svg/UndoCircleIcon";

const statusMap = {
    sent: {
        label: "ارسال شده",
        icon: <CircleCheckBig className="text-white" />,
        bg: "bg-[#6FEC94] border-green-100",
    },
    in_progress: {
        label: "در حال پردازش",
        icon: <UndoCircleIcon className="text-white" />,
        bg: "bg-gradient-to-l from-[#7BB8FF] to-[#32CCFE] border-blue-100",
    },
    done: {
        label: "تکمیل شده",
        icon: <ListCheck className="gray-red-600" />,
        bg: "bg-gray-300 border-gray-200",
    },
    reject: {
        label: "لغو شده",
        icon: <XCircle className="text-red-500" />,
        bg: "bg-red-100 border-red-200",
    },
} as const;

const OrdersListWrapper = () => {
    const activeFilter = ''; // استاتیک
    const filters = [
        { label: 'همه' },
        { label: 'در حال پردازش' },
        { label: 'ارسال شده' },
        { label: 'تکمیل شده' },
        { label: 'لغو شده' },
    ];

    // دیتای ثابت تستی
    const sampleInvoices = [
        {
            id: 1,
            invoice_number: "123456",
            creation_date: "1403/05/01",
            total: 250000,
            delivery_status: "sent",
            products: [
                { id: 1, image: "/test1.jpg" },
                { id: 2, image: "/test2.jpg" },
            ],
        },
        {
            id: 2,
            invoice_number: "654321",
            creation_date: "1403/05/02",
            total: 450000,
            delivery_status: "in_progress",
            products: [
                { id: 3, image: "/test3.jpg" },
            ],
        },
    ];

    return (
        <>
            {/* Header */}
            <div className="text-black flex items-center gap-3 p-3 h-20 px-5">
                <div className="p-4 px-4 h-full bg-gradient-to-t from-gray-100 to-transparent rounded-b-full">
                    <PackageBoxIcon size={25} />
                </div>
                <h1 className="md:text-xl">تاریخچه سفارشات</h1>
            </div>

            {/* Filters */}
            <div className="flex justify-between items-center p-3 px-5 w-full">
                <div className="flex flex-col md:flex-row gap-3 md:gap-5 items-center h-auto md:h-10">
                    <div className="flex items-center gap-2 max-md:hidden">
                        <FilterIcon />
                        <span>مرتب سازی:</span>
                    </div>

                    <div className="hidden md:flex md:gap-8 h-full">
                        {filters.map(({ label }) => (
                            <button key={label} className="transition-all">
                                {label}
                                <span className="mr-2 rounded-full text-sm px-2 border bg-[#F9FAFB] text-gray-500">
                                    2
                                </span>
                            </button>
                        ))}
                    </div>
                </div>

                {/* Search */}
                <div className="flex md:gap-8 items-center">
                    <div className="relative max-md:hidden">
                        <input
                            type="text"
                            placeholder="جستجو"
                            className="w-full py-3 bg-[#F9FAFB] pl-4 pr-10 text-gray-500 border rounded-3xl outline-none"
                        />
                        <div className="absolute left-5 top-1/2 transform -translate-y-1/2 cursor-pointer">
                            <Search className="w-5 h-5 text-gray-400" />
                        </div>
                    </div>
                    <p>{sampleInvoices.length} سفارش</p>
                </div>
            </div>

            {/* Orders List */}
            <div className="mt-3 p-3 px-5 min-h-[400px] flex flex-col">
                {sampleInvoices.map((invoice) => {
                    const status = invoice.delivery_status as keyof typeof statusMap;
                    const current = statusMap[status] || statusMap.done;

                    return (
                        <Link
                            href="#"
                            key={invoice.id}
                            className="md:bg-[#FAFAFA] md:p-3 rounded-2xl mb-4 flex gap-3 md:items-center max-md:flex-wrap"
                        >
                            {/* <div className="flex justify-between items-center">
                                <div className="flex gap-2 items-center">
                                    <div className={`w-9 h-9 ${current.bg} flex justify-center items-center rounded-full border-4`}>
                                        {current.icon}
                                    </div>
                                    <span className="!text-base !font-thin"> وضعیت: {current.label} </span>
                                </div>
                                <button className="text-gray-400 hover:text-gray-600">
                                    <Eye className="w-5 h-5" />
                                </button>
                            </div> */}
                            <div className="p-5 px-10 max-md:px-3 max-md:w-[40%] border-2 border-dashed border-gray-300 rounded-2xl flex justify-center items-center flex-col gap-3">
                                <div className="bg-primary text-white p-2 max-md:p-1.5 rounded-full">
                                    <RotateCcw size={30} className="border rounded-full p-1" />
                                </div>
                                <span>
                                    منتظر تایید
                                </span>
                                <button className="bg-[#363A3E] text-white flex items-center p-2 max-md:p-1.5 rounded-lg gap-1">
                                  <ShoppingCart size={22} />  مدیریت 
                                </button>
                            </div>
                            <div className="flex md:items-center max-md:w-[50%] md:gap-10 gap-3 mt-4 px-2 text-black max-md:flex-col md:hidden">
                                    <p className="text-sm"><span className="text-gray-600 ml-1">کد پیگیری:</span> #{invoice.invoice_number}</p>
                                    <p className="text-sm"><span className="text-gray-600 ml-1">تاریخ:</span> {invoice.creation_date}</p>
                                    <p className="text-sm"><span className="text-gray-600 ml-1">تعداد کالا:</span> - </p>
                                    <p className="text-sm flex gap-2"><span className="text-gray-600 ml-1">مبلغ:</span> {invoice.total.toLocaleString()} تومان <CircleCheckBig className="text-gray-600" size={17} /></p>
                                </div>
                            <div>
                                <div className="flex items-center md:gap-10 gap-3 mt-4 px-2 text-black max-md:flex-wrap max-md:hidden">
                                    <p className="text-sm"><span className="text-gray-600 ml-1">کد پیگیری:</span> #{invoice.invoice_number}</p>
                                    <p className="text-sm"><span className="text-gray-600 ml-1">تاریخ:</span> {invoice.creation_date}</p>
                                    <p className="text-sm"><span className="text-gray-600 ml-1">تعداد کالا:</span> - </p>
                                    <p className="text-sm flex gap-2"><span className="text-gray-600 ml-1">مبلغ:</span> {invoice.total.toLocaleString()} تومان <CircleCheckBig className="text-gray-600" size={17} /></p>
                                </div>

                                <div className="flex gap-5 max-md:gap-3 max-md:overflow-x-auto mt-8 px-2">

                                    {invoice.products.map((product) => (
                                        <div key={product.id} className="bg-white rounded-xl">
                                            <Image
                                                src={product.image}
                                                alt="product"
                                                className="object-cover h-[120px] rounded-xl"
                                                width={150}
                                                height={120}
                                            />
                                        </div>
                                    ))}
                                </div>
                            </div>


                        </Link>
                    );
                })}
            </div>
        </>
    );
};

export default OrdersListWrapper;
