'use server'

import { apiClient } from "@/lib/apiClient";

export async function getTicketDepartment() {
    try {
        const response = await apiClient("ticket-departments", {
            base: "alt",
            method: "GET",
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error getting departments:", error);
        return { success: false, error: "Failed to get departments" };
    }
}

export async function createTicket(formData: FormData) {
    try {
        const response = await apiClient("user/ticket", {
            base: "alt",
            method: "POST",
            body: formData,
        },true)
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error creating ticket:", error);
        return { success: false, error: "Failed to create ticket" };
    }
}

export async function getUserTcikets() {
    try {
        const response = await apiClient("user/ticket", {
            base: "alt",
            method: "GET"
        },true)
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error get tickets:", error);
        return { success: false, error: "Failed to get tickets" };
    }
}

export async function getTicketMessages(id: string) {
    try {
        const response = await apiClient(`user/ticket/${id}/messages`, {
            base: "alt",
            method: "GET"
        },true)
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error get ticket Messages:", error);
        return { success: false, error: "Failed to get ticket Messages" };
    }
}

export async function sendTicketMessage(id: number, message: string, status: string) {
    try {
        const response = await apiClient(`user/ticket/messages`, {
            base: "alt",
            method: "POST",
            body: {
                ticket_id: id,
                message,
                status
            }
        })
        const data = await response.json();
        return data
    } catch (error) {
        console.error("Error sending ticket message:", error);
        return { success: false, error: "Failed to send ticket message" };
    }
}

