"use client";
import { useState } from "react";

const TermsAndPoliciesContainer = () => {
  const [returnPolicy, setReturnPolicy] = useState("");
  const [warranty, setWarranty] = useState("");
  const [sellerNotes, setSellerNotes] = useState("");

  return (
    <div className="flex flex-col gap-6 p-6 bg-white rounded-2xl shadow-md">
      <h3 className="text-lg font-medium mb-4">شرایط و قوانین</h3>

      {/* سیاست بازگشت کالا */}
      <div className="flex flex-col gap-2">
        <label className="font-medium">سیاست بازگشت کالا</label>
        <textarea
          value={returnPolicy}
          onChange={(e) => setReturnPolicy(e.target.value)}
          placeholder="مثال: تا 7 روز پس از دریافت امکان بازگشت وجود دارد"
          className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary resize-none"
          rows={4}
        />
      </div>

      {/* ضمانت */}
      <div className="flex flex-col gap-2">
        <label className="font-medium">ضمانت</label>
        <input
          type="text"
          value={warranty}
          onChange={(e) => setWarranty(e.target.value)}
          placeholder="مثال: گارانتی 12 ماهه"
          className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
        />
      </div>

      {/* توضیحات فروشنده */}
      <div className="flex flex-col gap-2">
        <label className="font-medium">توضیحات فروشنده</label>
        <textarea
          value={sellerNotes}
          onChange={(e) => setSellerNotes(e.target.value)}
          placeholder="مثال: خدمات پس از فروش شامل ..."
          className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary resize-none"
          rows={4}
        />
      </div>
      <div className="flex justify-between items-center">
        <label className="flex gap-2 cursor-pointer" htmlFor="agreement">
            <input
                type="checkbox"
                id="agreement"
                className="w-4.5 h-4.5 accent-blue-600 cursor-pointer"
            />
            <span className='text-primary'>من قوانین و شرایط را مطالعه کردم و قبول دارم</span>
        </label>
      </div>
      <div className="flex justify-end">
        <button className="bg-primary cursor-pointer text-white px-5 py-3.5 rounded-xl">
          ثبت محصول
        </button>
      </div>
    </div>
  );
};

export default TermsAndPoliciesContainer;
