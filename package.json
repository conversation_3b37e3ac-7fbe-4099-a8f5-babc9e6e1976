{"name": "khodrox-seller", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tailwindcss/typography": "^0.5.16", "@tiptap/extension-link": "^3.0.9", "@tiptap/pm": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "clsx": "^2.1.1", "leaflet": "^1.9.4", "lucide-react": "^0.536.0", "next": "15.4.5", "nextjs-toploader": "^3.8.16", "rc-slider": "^11.1.8", "react": "19.1.0", "react-dom": "19.1.0", "react-hot-toast": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/leaflet": "^1.9.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4.1.11", "typescript": "^5"}}