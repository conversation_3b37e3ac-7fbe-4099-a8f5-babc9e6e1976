'use client'

import {useEffect} from 'react'

export default function Error({ error, reset }: {
    error: Error & { digest?: string }
    reset: () => void
}) {
    useEffect(() => {
        console.error(error)
    }, [error])

    return (
        <div className='h-screen w-full flex justify-center items-center'>
            <div className='w-full px-5 border border-border md:px-10 py-10 rounded-3xl bg-white shadow-lg'>
                <div className='w-full flex justify-center items-center flex-col'>
                    <h2>با عرض پوزش مشکلی در سایت پیش آمد</h2>
                    <button
                        onClick={
                            // Attempt to recover by trying to re-render the segment
                            () => reset()
                        }
                    >
                        تلاش مجدد
                    </button>
                </div>
            </div>
        </div>
    )
}