import React from 'react';
interface ChatOutlineIconProps {
    size?: number;
    color?: string;
    className?: string;
    stroke?: string;
}

const ChatAcceptIcon: React.FC<ChatOutlineIconProps> = ({
    size = 24,
    color = '#fff',
    className = '',
    stroke = 'none',
}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill={color}
            aria-hidden="true"
            className={className}
            stroke={stroke}
        >
            <path
                d="M8.219,9.164a.75.75,0,0,0-.937,1.171ZM9.5,11.152l.469-.586Zm1.377-.122-.564-.494h0Zm3.434-2.786a.75.75,0,1,0-1.129-.988ZM9.75,1.5h2V0h-2ZM1.5,14.75v-5H0v5ZM11.75,18h-7v1.5h7ZM0,14.75A4.75,4.75,0,0,0,4.75,19.5V18A3.25,3.25,0,0,1,1.5,14.75Zm20-5A8.25,8.25,0,0,1,11.75,18v1.5A9.75,9.75,0,0,0,21.5,9.75ZM11.75,1.5A8.25,8.25,0,0,1,20,9.75h1.5A9.75,9.75,0,0,0,11.75,0ZM9.75,0A9.75,9.75,0,0,0,0,9.75H1.5A8.25,8.25,0,0,1,9.75,1.5ZM7.281,10.336l1.753,1.4.937-1.171-1.753-1.4Zm4.163,1.188,2.87-3.28-1.129-.988-2.87,3.28Zm-2.41.214a1.75,1.75,0,0,0,2.41-.214l-1.129-.988a.25.25,0,0,1-.344.031Z"
                transform="translate(1.25 2.25)"
                fill={color}
            />
        </svg>
    );
};

export default ChatAcceptIcon;