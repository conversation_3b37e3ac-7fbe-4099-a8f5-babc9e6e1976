import React from "react";

interface MoneyBagIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
  color?: string;
}

const MoneyBagIcon: React.FC<MoneyBagIconProps> = ({
  size = 24,
  color = "currentColor",
  className,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
      {...props}
    >
      <path
        d="M15.8,7.853l-.712.237Zm-1-3-.712.237ZM7.755,1.5H9.106V0H7.755Zm6.331,3.59,1,3,1.423-.474-1-3ZM10.106,15H6.755v1.5h3.351ZM1.775,8.09l1-3L1.352,4.615l-1,3ZM6.755,15A5.25,5.25,0,0,1,1.775,8.09L.352,7.615a6.75,6.75,0,0,0,6.4,8.885Zm8.331-6.91A5.25,5.25,0,0,1,10.106,15v1.5a6.75,6.75,0,0,0,6.4-8.885ZM9.106,1.5a5.25,5.25,0,0,1,4.981,3.59l1.423-.474A6.75,6.75,0,0,0,9.106,0ZM7.755,0a6.75,6.75,0,0,0-6.4,4.615l1.423.474A5.25,5.25,0,0,1,7.755,1.5Z"
        transform="translate(3.569 6.25)"
        fill={color}
      />
      <path
        d="M6.847,5.75V6.5a.75.75,0,0,0,.565-.257Zm-4.172,0-.565.493a.75.75,0,0,0,.565.257ZM4.436,1.159l.23-.714Zm.65,0L4.856.445h0ZM8.248,4.143,7.683,3.65h0ZM6.014.859,5.784.146ZM1.274,4.143l-.565.493ZM3.508.859l-.23.714ZM6.847,5H2.675V6.5H6.847Zm-3.606.257L1.84,3.65.709,4.635l1.4,1.607ZM7.683,3.65l-1.4,1.607,1.131.986,1.4-1.607Zm-4.4-2.076.928.3L4.666.445l-.928-.3Zm2.039.3.928-.3L5.784.146l-.928.3Zm-1.111,0a1.81,1.81,0,0,0,1.111,0L4.856.445a.31.31,0,0,1-.19,0ZM8.814,4.635A2.808,2.808,0,0,0,5.784.146l.461,1.428A1.308,1.308,0,0,1,7.683,3.65ZM1.84,3.65A1.308,1.308,0,0,1,3.278,1.573L3.739.146a2.808,2.808,0,0,0-3.03,4.49Z"
        transform="translate(7.239 1.25)"
        fill={color}
      />
      <path
        d="M1.128.1A.75.75,0,0,0,.372,1.4Zm5.986,1.3A.75.75,0,1,0,6.386.094ZM.372,1.4A6.6,6.6,0,0,0,3.684,2.5a7.02,7.02,0,0,0,3.43-1.091L6.386.094a5.586,5.586,0,0,1-2.7.9A5.152,5.152,0,0,1,1.128.1Z"
        transform="translate(8.25 16.25)"
        fill={color}
      />
    </svg>
  );
};

export default MoneyBagIcon;
