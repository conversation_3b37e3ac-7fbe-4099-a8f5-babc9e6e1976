import ProductsListItem from '@/components/ProductsList/ProductsListItem';
import { BadgeCheck, PencilLine, PlusCircle, SlidersHorizontal, Store, Zap } from 'lucide-react'

interface SortOption {
    label: string;
}

const sortOptions: SortOption[] = [
    { label: "فعال" },
    { label: "پیش نویس" },
    { label: "غیرفعال" },
];
const ProductListPage = () => {
    return (
        <div className='max-md:px-3'>
            <div className='bg-white rounded-xl p-3 px-5'>
                <div className='flex items-center justify-between'>
                    <div className='flex items-center gap-3 '>
                        <div className='bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3  rounded-b-4xl'>
                            <Store />
                        </div>
                        <h1 className='md:text-lg text-base'> محصولات فروشگاه من </h1>
                    </div>
                    <div className='flex gap-3'>
                        <button className='bg-white border border-gray-200 text-primary flex items-center gap-2 flex-row-reverse font-light px-5 py-3 rounded-xl cursor-pointer text-sm'> افزودن محصول <PlusCircle size={18} /> </button>
                    </div>

                </div>
                <div className="bg-white flex-1 max-md:max-w-fit  py-8 w-full md:col-span-8 mr-auto gap-3 flex items-center  rounded-3xl relative">
                    {/* Desktop Label */}
                    <div className="hidden md:flex gap-1 text-black items-center whitespace-nowrap md:text-base text-xs font-bold">
                        <SlidersHorizontal size={16} />
                        مرتب سازی:
                    </div>

                    {/* Desktop Sort Options */}
                    <ul className="hidden md:flex flex-wrap gap-0 lg:gap-x-1 md:text-base text-sm">
                        {sortOptions.map((option) => (
                            <li
                                key={option.label}
                                className="cursor-pointer text-[#5E646B] py-2 px-3 whitespace-nowrap md:text-base text-xs font-light rounded-3xl"
                            >
                                {option.label}
                            </li>
                        ))}
                    </ul>

                    {/* Mobile Button */}
                    <div className="md:hidden max-w-fit relative w-full">
                        <button className="w-full max-md:py-1 md:px-2 py-2 rounded-3xl gap-x-[4px] flex justify-between items-center text-sm">
                            <SlidersHorizontal size={16} />
                            <span className="max-md:text-xs whitespace-nowrap">مرتب سازی</span>
                        </button>
                    </div>

                    {/* Product Count */}
                    <div className="bg-gradient-to-l max-lg:hidden absolute top-1/2 transform -translate-y-1/2 w-[150px] left-0 from-gray-100 to-transparent mr-20 py-3 rounded-3xl">
                        <div className="flex justify-center items-center w-full">
                            <span className="text-gray-800 text-sm">(31)</span>
                            <span className="text-[#9DA5B0] text-sm mr-1">محصول</span>
                        </div>
                    </div>
                </div>

            </div>
            <div className='mt-5 grid grid-cols-3 max-md:grid-cols-1 gap-5 '>
                <ProductsListItem />
                <ProductsListItem />
                <ProductsListItem />
                <ProductsListItem />
                <ProductsListItem />
                <ProductsListItem />
            </div>


        </div>
    )
}

export default ProductListPage