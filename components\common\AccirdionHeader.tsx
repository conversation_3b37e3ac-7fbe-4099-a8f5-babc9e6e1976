"use client";
import { useEffect, useState } from "react";
import { ChevronUp, ChevronDown, X } from "lucide-react";

interface AccordionHeaderProps {
    title: string;
    children: React.ReactNode;
    titleSize?: string
    open?: boolean;
    showToggleBtn?: boolean;
    showCloseBtn?: boolean;
    onCloseBtnClick?: () => void;
    buttonStyles?: string
}

const AccordionHeader: React.FC<AccordionHeaderProps> = ({
    title,
    children,
    titleSize,
    buttonStyles,
    open = true,
    onCloseBtnClick,
    showToggleBtn = true,
    showCloseBtn = false
}) => {
    const [isOpen, setIsOpen] = useState<boolean>(open);

    useEffect(() => {
        if (window.innerWidth < 768 && title !== "محدوده قیمت") {
            setIsOpen(open ? open : false);
        }
    }, [open, title]);

    return (
        <>
            <div
                onClick={() => {
                    if (!showToggleBtn) return
                    setIsOpen(!isOpen)
                }}
                className="relative pb-3 cursor-pointer pt-3 max-md:flex justify-between max-md:flex-col-reverse gap-3 max-md:pt-0"
            >
                <h3 className={` text-[#363A3E] ${titleSize ?? "text-sm"}  relative py-2.5`}>{title}</h3>
                {
                    showToggleBtn && <button
                        onClick={() => setIsOpen(!isOpen)}
                        className={`absolute ${buttonStyles ?? "top-[-17px]"} left-[10px] bg-gradient-to-t w-[40px] from-gray-100 to-transparent rounded-full pt-5 h-14 flex items-center justify-center p-2 transition-transform duration-300`}>
                        {isOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />
                        }
                    </button>
                }
                {
                    showCloseBtn &&
                    // <button
                    //     onClick={() => onCloseBtnClick?.()}
                    //     className="absolute top-[-17px] left-[10px] bg-gradient-to-t w-[40px] from-gray-100 to-transparent rounded-full
                    //      pt-5 h-14 flex items-center justify-center p-2 transition-transform duration-300">
                    //     <X className="w-4 h-4"/>
                    // </button>
                    <button
                        onClick={() => onCloseBtnClick?.()}
                        className=" bg-gradient-to-t w-[40px] from-gray-100 to-transparent rounded-full
                         pt-5 h-14 flex items-center justify-center p-2 transition-transform duration-300">
                        <X className="w-4 h-4" />
                    </button>
                }
            </div>


            <div
                className={`transition-max-height duration-300 overflow-hidden ${isOpen ? "max-h-fit opacity-100" : "max-h-0 opacity-0"
                    }`}
            >
                <div className="mt-5">{children}</div>
            </div>
        </>
    );
};

export default AccordionHeader;
