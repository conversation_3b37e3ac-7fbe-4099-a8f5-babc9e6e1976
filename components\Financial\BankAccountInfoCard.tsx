import { <PERSON>geCheck } from "lucide-react";
import React from "react";
import MeliBank from "@/public/assets/images/5.png";
import MelatBank from "@/public/assets/images/Melaat_bank.png";
import Image from "next/image";
const BankAccountInfoCard = () => {
    return (
        <div className="flex  md:gap-8 gap-3 items-center border border-gray-200 rounded-2xl p-5 max-md:px-1">
            <div className=" bg-gray-100/50 p-3 px-5 max-md:px-3 rounded-2xl border-dashed border-gray-300 border-2">
                <Image src={MeliBank} width={50} height={50} alt="product" className="max-md:w-10" />
            </div>
            <div className="flex gap-1 md:divide-x divide-gray-300 max-md:flex-col">
                {/* <div className="md:hidden bg-gray-100/50 p-3 px-5 max-md:px-3 rounded-2xl border-dashed border-gray-300 border-2">
                    <Image src={MeliBank} width={50} height={50} alt="product" />
                </div> */}
                <div className="md:px-5 max-md:flex max-md:justify-between max-md:gap-1 max-md:text-sm font-light ">
                    <p>نام بانک</p>
                    <p>ملی</p>
                </div>
                <div className="md:px-5 max-md:flex max-md:justify-between max-md:gap-1 max-md:text-sm font-light">
                    <p>شماره حساب</p>
                    <p>***********</p>
                </div>
                <div className="md:px-5 max-md:flex max-md:justify-between max-md:gap-1 max-md:text-sm font-light">
                    <p>شماره کارت</p>
                    <p>**************</p>
                </div>
                <div className="md:px-5 max-md:flex max-md:justify-between max-md:gap-1 max-md:text-sm font-light">
                    <p>شماره شبا</p>
                    <p className="flex items-center gap-3 ">
                        IR-1234567891234567891234
                        <span className="max-md:hidden flex items-center gap-1 bg-green-500 text-sm text-white font-bold border border-green-400 p-1 px-3 rounded-lg">
                            <BadgeCheck size={20} className="text-white" stroke="white" />{" "}
                            <span> تایید شده </span>
                        </span>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default BankAccountInfoCard;
