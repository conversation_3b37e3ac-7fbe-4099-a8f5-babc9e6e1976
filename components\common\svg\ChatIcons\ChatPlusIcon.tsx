import React from "react";

interface ChatPlusIconProps {
  width?: number | string;
  height?: number | string;
  fill?: string;
  className?: string;
}

const ChatPlusIcon: React.FC<ChatPlusIconProps> = ({
  width = 24,
  height = 24,
  fill = "#9da5b0",
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width={width}
      height={height}
      className={className}
      fill="none"
    >
      <path
        d="M11.5 6.75a.75.75 0 0 0-1.5 0Zm-1.5 6a.75.75 0 0 0 1.5 0Zm3.75-2.25a.75.75 0 0 0 0-1.5ZM7.75 9a.75.75 0 0 0 0 1.5Zm2-7.5h2V0h-2ZM1.5 14.75v-5H0v5ZM11.75 18h-7v1.5h7ZM0 14.75A4.75 4.75 0 0 0 4.75 19.5V18A3.25 3.25 0 0 1 1.5 14.75Zm20-5A8.25 8.25 0 0 1 11.75 18v1.5A9.75 9.75 0 0 0 21.5 9.75ZM11.75 1.5A8.25 8.25 0 0 1 20 9.75h1.5A9.75 9.75 0 0 0 11.75 0ZM9.75 0A9.75 9.75 0 0 0 0 9.75H1.5A8.25 8.25 0 0 1 9.75 1.5ZM10 6.75v6h1.5v-6ZM13.75 9h-6v1.5h6Z"
        transform="translate(1.25 2.25)"
        fill={fill}
      />
    </svg>
  );
};

export default ChatPlusIcon;
