import React from 'react';

interface ChatOutlineIconProps {
    size?: number;
    color?: string;
    className?: string;
    stroke?: string;
}

const ChatLockIcon: React.FC<ChatOutlineIconProps> = ({
    size = 24,
    color = '#fff',
    className = '',
    stroke = 'none',
}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill={color}
            aria-hidden="true"
            className={className}
            stroke={stroke}
        >
            <path
                d="M9.75,1.5h2V0h-2ZM1.5,14.75v-5H0v5ZM11.75,18h-7v1.5h7ZM0,14.75A4.75,4.75,0,0,0,4.75,19.5V18A3.25,3.25,0,0,1,1.5,14.75Zm20-5A8.25,8.25,0,0,1,11.75,18v1.5A9.75,9.75,0,0,0,21.5,9.75ZM11.75,1.5A8.25,8.25,0,0,1,20,9.75h1.5A9.75,9.75,0,0,0,11.75,0ZM9.75,0A9.75,9.75,0,0,0,0,9.75H1.5A8.25,8.25,0,0,1,9.75,1.5ZM9.5,8.75v-1H8v1Zm2.5-1v1h1.5v-1ZM10.75,6.5A1.25,1.25,0,0,1,12,7.75h1.5A2.75,2.75,0,0,0,10.75,5ZM9.5,7.75A1.25,1.25,0,0,1,10.75,6.5V5A2.75,2.75,0,0,0,8,7.75ZM8.75,9.5h4V8h-4ZM14,10.75v1h1.5v-1ZM12.75,13h-4v1.5h4ZM7.5,11.75v-1H6v1ZM8.75,13A1.25,1.25,0,0,1,7.5,11.75H6A2.75,2.75,0,0,0,8.75,14.5ZM14,11.75A1.25,1.25,0,0,1,12.75,13v1.5a2.75,2.75,0,0,0,2.75-2.75ZM12.75,9.5A1.25,1.25,0,0,1,14,10.75h1.5A2.75,2.75,0,0,0,12.75,8ZM8.75,8A2.75,2.75,0,0,0,6,10.75H7.5A1.25,1.25,0,0,1,8.75,9.5Z"
                transform="translate(1.25 2.25)"
                fill={color}
            />
        </svg>
    );
};

export default ChatLockIcon;