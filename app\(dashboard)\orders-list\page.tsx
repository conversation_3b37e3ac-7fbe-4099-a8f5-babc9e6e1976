
// import { AllInvoicesResponse } from "@/lib/types/invoice.types"
import OrdersListWrapper from "@/components/OrdersList/OrdersListWrapper"
import { apiClient } from "@/lib/apiClient"


const OrdersListPage = async () => {
//   const response = await apiClient("invoices", {
//     base: "alt",
//     method: "GET",
//   })
//   const invoiceResponse: AllInvoicesResponse = await response.json()
//   const invoices = invoiceResponse?.data?.invoices ?? []
//   console.log(invoiceResponse);
//   const delivery_status_counts = invoiceResponse.data.delivery_status_counts
  
  return (
    <section className="bg-white rounded-xl">
      <OrdersListWrapper />
    </section>
  )
}

export default OrdersListPage