import { Ticket, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Clock, XCircle } from "lucide-react";
import Link from "next/link";


const statusMap = {
  paid: {
    label: "پرداخت موفق",
    color: "text-green-600",
    bg: "bg-green-100 border-green-300",
    icon: <CheckCircle className="w-5 h-5 text-green-600" />,
  },
  pending: {
    label: "در انتظار",
    color: "text-yellow-600",
    bg: "bg-yellow-100 border-yellow-300",
    icon: <Clock className="w-5 h-5 text-yellow-600" />,
  },
  failed: {
    label: "ناموفق",
    color: "text-red-600",
    bg: "bg-red-100 border-red-300",
    icon: <XCircle className="w-5 h-5 text-red-600" />,
  },
};


const invoices = [
  {
    id: 1,
    creation_date: "1404/06/01",
    total: 850000,
    account_number: "6037-****-2584",
    tracking_code: "TRX58412",
    status: "paid",
  },
  {
    id: 2,
    creation_date: "1404/05/28",
    total: 1200000,
    account_number: "5894-****-1025",
    tracking_code: "TRX87452",
    status: "pending",
  },
  {
    id: 3,
    creation_date: "1404/05/20",
    total: 450000,
    account_number: "6104-****-4421",
    tracking_code: "TRX99874",
    status: "failed",
  },
  {
    id: 4,
    creation_date: "1404/05/10",
    total: 2300000,
    account_number: "6273-****-7751",
    tracking_code: "TRX66541",
    status: "paid",
  },
];

const TransactionsList = () => {
  return (
    <div
      className="w-full h-auto mt-10 bg-white shadow-md rounded-3xl overflow-hidden px-2 max-md:order-5 py-5"
      dir="rtl"
    >
      
      
      {/* Responsive Table Container */}
      <div className="w-full mx-auto overflow-x-auto px-2 rounded-3xl p-1">
        <div className="hidden md:block">
          {/* Table Header */}
          <div className="grid grid-cols-12 bg-[#9DA5B0] text-white rounded-xl mb-3 px-3">
            <div className="py-3  text-right font-medium col-span-1">
              ردیف
            </div>
            <div className="py-3  text-right font-medium col-span-2">
              تاریخ
            </div>
            <div className="py-3  text-right font-medium col-span-2">
              مبلغ (تومان)
            </div>
            <div className="py-3  text-right font-medium col-span-2">
              شماره حساب
            </div>
            <div className="py-3  text-right font-medium col-span-2">
              کد رهگیری
            </div>
            <div className="py-3  text-right font-medium col-span-2">
              وضعیت
            </div>
          </div>

          {/* Table Rows */}
          {invoices.map((invoice, index) => {
            const status = invoice.status as keyof typeof statusMap;
            const current = statusMap[status] ?? statusMap.pending;

            return (
              <Link              
                href={`transactions/${invoice.id}`}

                key={invoice.id}
                className="px-3 grid grid-cols-12 text-right bg-white odd:bg-gray-100 hover:bg-gray-50 transition rounded-xl overflow-hidden"
              >
                <div className="py-4  text-right col-span-1">
                  {index + 1}
                </div>
                <div className="py-4  text-right col-span-2">
                  {invoice.creation_date}
                </div>
                <div className="py-4  text-right col-span-2">
                  {invoice.total.toLocaleString()}
                </div>
                <div className="py-4  text-right col-span-2">
                  {invoice.account_number}
                </div>
                <div className="py-4  text-right col-span-2">
                  {invoice.tracking_code}
                </div>
                <div className="py-4  text-right col-span-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div
                        className={`w-9 h-9 ${current.bg} flex justify-center items-center rounded-full border-4`}
                      >
                        {current.icon}
                      </div>
                      <span className={current.color}>{current.label}</span>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600">
                      <Eye className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Mobile View - Card Layout */}
        <div className="md:hidden flex flex-col gap-3">
          {invoices.map((invoice) => {
            const status = invoice.status as keyof typeof statusMap;
            const current = statusMap[status] ?? statusMap.pending;

            return (
              <Link
                href={`transactions/${invoice.id}`}
                key={invoice.id}
                className="even:bg-gray-50 p-4 rounded-lg shadow-sm flex flex-col gap-2"
              >
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">
                    شناسه سفارش: {invoice.id}
                  </span>
                  <button className="text-gray-400 hover:text-gray-600">
                    <Eye className="w-5 h-5" />
                  </button>
                </div>
                <div className="text-gray-700 text-sm">
                  تاریخ: {invoice.creation_date}
                </div>
                <div className="text-gray-900 font-semibold">
                  مبلغ: {invoice.total.toLocaleString()} تومان
                </div>
                <div className="text-gray-700 text-sm">
                  حساب: {invoice.account_number}
                </div>
                <div className="text-gray-700 text-sm">
                  رهگیری: {invoice.tracking_code}
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-9 h-9 ${current.bg} flex justify-center items-center rounded-full border-4`}
                    >
                      {current.icon}
                    </div>
                    <span className={current.color}>{current.label}</span>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default TransactionsList;
