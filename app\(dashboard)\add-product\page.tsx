import AddNewProductContainer from "@/components/Products/Add/AddNewProductContainer";
import DeliveryMethodsContainer from "@/components/Products/Delivery/DeliveryMethodsContainer";
import ProductFormContainer from "@/components/Products/ProductDetails/ProductFormContainer";
import TermsAndPoliciesContainer from "@/components/Products/TermsAndPolicies/TermsAndPoliciesContainer";
import ProductVarients from "@/components/Products/Varients/ProductVarients";

const page = async ({ searchParams }: { searchParams: Promise<{ step?: string }>} ) => {
  const { step } = await searchParams; 
  const currentStep = step ?? "1"; 
  console.log(currentStep);
     

  if (currentStep == "1") {
    return <AddNewProductContainer />
    
  }
  if (currentStep == "2") {
    return <ProductFormContainer />
  }
  if (currentStep == "3") {
    return <ProductVarients />    
  }
  if (currentStep == "4") {
    return <DeliveryMethodsContainer />    
  }
  if (currentStep == "5") {
    return <TermsAndPoliciesContainer />    
  }

  return (
    <div>
      <AddNewProductContainer /> :
    </div>
  );
};

export default page;
