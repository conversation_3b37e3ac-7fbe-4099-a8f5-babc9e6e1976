import Image from 'next/image'
import Money from '@/public/assets/images/money.png'
import BankDropdown from './BankDropdown'
import WithdrawRequestList from './WithdrawRequestList'

const WithdrawRequest = () => {
  return (
    <section className='md:grid grid-cols-12 mt-5 gap-8 max-md:px-3 space-y-5'>
        <div className='col-span-5 bg-white rounded-2xl border border-gray-200 p-5 flex justify-center items-center flex-col'>
            <div className='relative w-40 h-40'>
                <Image src={Money} fill alt="product" />
            </div>
            <div className='text-center flex flex-col gap-3'>
                <p className='font-light'>
                    موجودی قابل برداشت
                </p>
                <h3 className='font-bold text-xl'>
                    ******** <span className='text-sm font-medium'>تومان</span>
                </h3>
            </div>
            <div className='flex flex-col gap-5 mt-8 w-full'>
                <div className='flex flex-col gap-2'>
                    <label className='px-1 text-sm' htmlFor="amount">
                        مبلغ درخواستی (تومان)
                    </label>
                    <div className='relative'>
                        <input type="number" id="amount" className='w-full border border-gray-300 rounded-2xl p-3 focus:outline-none focus:ring-primary focus:border-primary h-16'  />
                        <span className='absolute left-5 top-1/2 transform -translate-y-1/2 cursor-pointer bg-muted px-3 py-1 rounded-2xl border border-gray-300 text-gray-500' autoCorrect='off' aria-expanded='false' >همه</span>
                    </div>
                </div>
                <div className='flex flex-col gap-2'>
                    <label htmlFor="bank" className='px-1 text-sm'>
                        انتخاب بانک
                    </label>
                    <BankDropdown />
                </div>
            </div>

        </div>
        <div className='col-span-7 bg-white rounded-2xl border border-gray-200 p-5'>
            <WithdrawRequestList />
        </div>

    </section>
  )
}

export default WithdrawRequest