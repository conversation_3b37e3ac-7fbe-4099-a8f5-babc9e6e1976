// import { cn } from "@/lib/utils";
// import { <PERSON><PERSON><PERSON><PERSON>oa<PERSON> } from "react-spinners";

type LoadingProps = {
    className?: string;
};

export default function Loading({ className }: LoadingProps) {
    // return (
    //     <div className={cn("flex flex-col justify-center items-center h-screen w-screen", className)}>
    //         <BounceLoader
    //             color="#d5af0e"
    //             loading
    //         />
    //         <p className='text-xs text-neutral-500 mt-4'>لطفا منتظر بمانید</p>
    //     </div>
    // );
    return (
        <div className="flex flex-col gap-5 justify-center items-center h-[90vh]">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />            
        </div>
    )
}