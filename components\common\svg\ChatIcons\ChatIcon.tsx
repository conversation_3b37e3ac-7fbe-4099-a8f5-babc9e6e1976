import React from "react"

interface ChatIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
}

const ChatIcon: React.FC<ChatIconProps> = ({
  size = 25,
  color = "#9da5b0",
  ...props
}) => (
  <svg
    width={size}
    height={(size * 22.465) / 24.961} // maintain original aspect ratio
    viewBox="0 0 24.961 22.465"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g transform="translate(0)">
      <path
        d="M13.729 0h-2.5A11.233 11.233 0 0 0 0 11.233v6.24a4.992 4.992 0 0 0 4.992 4.992h8.736A11.233 11.233 0 0 0 13.729 0Z"
        fill={color}
        opacity="0.4"
      />
      <path
        d="M0 .428a.936.936 0 0 0 .936.936h4.992a.936.936 0 1 0 0-1.872H.936A.936.936 0 0 0 0 .428ZM0-4.564a.936.936 0 0 0 .936.936h9.984a.936.936 0 0 0 .936-.936.936.936 0 0 0-.936-.936H.936A.936.936 0 0 0 0-4.564Z"
        transform="translate(6.552 13.3)"
        fill={color}
        fillRule="evenodd"
      />
    </g>
  </svg>
)

export default ChatIcon
