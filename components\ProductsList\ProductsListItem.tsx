import { <PERSON>ge<PERSON>heck, <PERSON><PERSON><PERSON><PERSON><PERSON>, Zap } from 'lucide-react'
import Image from 'next/image';
import ProductImage from "@/public/assets/images/motor-oil.png"
const ProductsListItem = () => {
  return (
    <div className='col-span-1 bg-white p-3 rounded-xl px-4'>
                    <div className='relative w-full h-52'>
                        <Image src={ProductImage} fill alt='product' />
                        <span className='bg-[#2DC058] px-3 py-1.5 rounded-lg text-white absolute top-4 left-3 flex items-center gap-1 text-sm'> <BadgeCheck size={18} /> مهر تایید  </span>
                    </div>
                    <div className='card-details flex flex-col gap-3'>
                        <h3 className='mt-5 pb-3 font-medium'>
                            عنوان محصول
                        </h3>
                        <p className='flex gap-1'>
                            <span className='font-light'>
                                شناسه محصول: 
                            </span>
                            <span>
                                31646413
                            </span>
                        </p>
                        <p className='flex gap-1'>
                            <span className='font-light'>
                                قیمت کالا: 
                            </span>
                            <span className='font-bold'>
                                6500000 <span className='!font-light'>تومان</span>
                            </span>
                        </p>
                        <div className='flex items-center justify-between'>
                            <button className='bg-muted flex items-center gap-2 border border-gray-200 rounded-xl py-2 px-5 w-[40%] text-right'>
                                <PencilLine size={18} />
                                ویرایش
                            </button>
                            <button className='bg-muted flex items-center gap-2 border border-gray-200 rounded-xl py-2 px-5 w-[55%] text-right'>
                                <Zap fill='#F7BC06' color='#F7BC06' size={18} />
                                افزایش فروش
                            </button>
                            
                        </div>

                    </div>
                </div>
  )
}

export default ProductsListItem