"use client";
import { FilePlus } from "lucide-react";
import { useRouter } from "nextjs-toploader/app";
import { useState } from "react";

const shippingOptions = ["پست پیشتاز", "پیک داخلی", "تیپاکس"];

const DeliveryMethodsContainer = () => {
    const router = useRouter()
  const [weight, setWeight] = useState("");
  const [dimensions, setDimensions] = useState({
    length: "",
    width: "",
    height: "",
  });
  const [shippingMethod, setShippingMethod] = useState("");
  const [readyTime, setReadyTime] = useState("");
  const [shippingCost, setShippingCost] = useState("");

  return (
    <div className="flex flex-col gap-6 p-6 max-md:px-3 bg-white rounded-2xl shadow-md">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3 rounded-b-4xl">
            <FilePlus />
          </div>
          <div className="flex items-center gap-1">
            <h2 className="text-lg font-medium">اطلاعات مرسوله و بسته بندی</h2>
          </div>
        </div>
      </div>

      <div className="grid max-md:px-1 grid-cols-8 max-md:grid-cols-4 md:gap-8 max-md:gap-5 max-md:gap-y-8 border-b border-b-gray-300 pb-8">
        {/* ابعاد بسته‌بندی */}
        {["طول", "عرض", "ارتفاع"].map((dim) => (
          <div key={dim} className="col-span-2 flex flex-col gap-2 ">
            <label>{dim} بسته‌بندی (cm)</label>
            <input
              type="number"
              value={dimensions[dim.toLowerCase() as keyof typeof dimensions]}
              onChange={(e) =>
                setDimensions({
                  ...dimensions,
                  [dim.toLowerCase()]: e.target.value,
                })
              }
              placeholder="مثال: 20"
              className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
            />
          </div>
        ))}
        {/* وزن محصول */}
        <div className="col-span-2 flex flex-col gap-2">
          <label>وزن محصول (گرم)</label>
          <input
            type="number"
            value={weight}
            onChange={(e) => setWeight(e.target.value)}
            placeholder="مثال: 1000"
            className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
          />
        </div>

        {/* <div className="col-span-2 flex gap-4">
        </div> */}
      </div>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center gap-3">
          {/* <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3 rounded-b-4xl">
            <FilePlus />
          </div> */}
          <div className="flex items-center gap-1">
            <h2 className="text-lg font-medium"> روش‌های ارسال <span className=" text-sm">(حداقل یک روش انتخاب کنید)</span> </h2>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-8 max-md:divide-y max-md:divide-gray-300 ">
        <div className="md:grid grid-cols-3 gap-8 max-md:space-y-7 max-md:pb-10">
          {/* روش ارسال */}
          <div className="md:col-span-1 flex flex-col gap-2 ">
            <label>روش ارسال</label>
            <select
              value={shippingMethod}
              onChange={(e) => setShippingMethod(e.target.value)}
              className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
            >
              <option value="">انتخاب کنید</option>
              {shippingOptions.map((s) => (
                <option key={s} value={s}>
                  {s}
                </option>
              ))}
            </select>
          </div>

          {/* زمان آماده‌سازی */}
          <div className="col-span-1 flex flex-col gap-2 ">
            <label>زمان آماده‌سازی (ساعت)</label>
            <input
              type="number"
              value={readyTime}
              onChange={(e) => setReadyTime(e.target.value)}
              placeholder="مثال: 24"
              className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
            />
          </div>

          {/* هزینه ارسال */}
          <div className="col-span-1 flex flex-col gap-2 ">
            <label>هزینه ارسال (تومان)</label>
            <input
              type="number"
              value={shippingCost}
              onChange={(e) => setShippingCost(e.target.value)}
              placeholder="مثال: 15000"
              className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
            />
          </div>
        </div>
        <div className="md:grid grid-cols-3 gap-8 max-md:space-y-7">
          {/* روش ارسال */}
          <div className="md:col-span-1 flex flex-col gap-2 ">
            <label>روش ارسال</label>
            <select
              value={shippingMethod}
              onChange={(e) => setShippingMethod(e.target.value)}
              className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
            >
              <option value="">انتخاب کنید</option>
              {shippingOptions.map((s) => (
                <option key={s} value={s}>
                  {s}
                </option>
              ))}
            </select>
          </div>

          {/* زمان آماده‌سازی */}
          <div className="col-span-1 flex flex-col gap-2 ">
            <label>زمان آماده‌سازی (ساعت)</label>
            <input
              type="number"
              value={readyTime}
              onChange={(e) => setReadyTime(e.target.value)}
              placeholder="مثال: 24"
              className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
            />
          </div>

          {/* هزینه ارسال */}
          <div className="col-span-1 flex flex-col gap-2 ">
            <label>هزینه ارسال (تومان)</label>
            <input
              type="number"
              value={shippingCost}
              onChange={(e) => setShippingCost(e.target.value)}
              placeholder="مثال: 15000"
              className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary"
            />
          </div>
        </div>
      </div>
      
      <div className="flex justify-end">
        <button onClick={() => router.push("/add-product?step=5")} className="bg-primary cursor-pointer text-white px-5 py-3.5 rounded-xl">
          ثبت و ذخیره
        </button>
      </div>
    </div>
  );
};

export default DeliveryMethodsContainer;
