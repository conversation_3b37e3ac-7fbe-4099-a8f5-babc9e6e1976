import React from 'react';

interface ChatOutlineIconProps {
    size?: number;
    color?: string;
    className?: string;
    stroke?: string;
}

const ChatSearchIcon: React.FC<ChatOutlineIconProps> = ({
    size = 24,
    color = '#fff',
    className = '',
    stroke = 'none',
}) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={size}
            height={size}
            viewBox="0 0 24 24"
            fill={color}
            aria-hidden="true"
            className={className}
            stroke={stroke}
        >
            <path
                d="M13.78,11.72A.75.75,0,0,0,12.72,12.78ZM13.72,13.78A.75.75,0,0,0,14.78,12.72ZM9.75,1.5h2V0h-2ZM1.5,14.75v-5H0v5ZM11.75,18h-7v1.5h7ZM0,14.75A4.75,4.75,0,0,0,4.75,19.5V18A3.25,3.25,0,0,1,1.5,14.75Zm20-5A8.25,8.25,0,0,1,11.75,18v1.5A9.75,9.75,0,0,0,21.5,9.75ZM11.75,1.5A8.25,8.25,0,0,1,20,9.75h1.5A9.75,9.75,0,0,0,11.75,0ZM9.75,0A9.75,9.75,0,0,0,0,9.75H1.5A8.25,8.25,0,0,1,9.75,1.5ZM13,9.75A2.25,2.25,0,0,1,10.75,12v1.5A3.75,3.75,0,0,0,14.5,9.75ZM10.75,12A2.25,2.25,0,0,1,8.5,9.75H7a3.75,3.75,0,0,0,3.75,3.75ZM8.5,9.75A2.25,2.25,0,0,1,10.75,7.5V6A3.75,3.75,0,0,0,7,9.75ZM10.75,7.5A2.25,2.25,0,0,1,13,9.75h1.5A3.75,3.75,0,0,0,10.75,6Zm1.97,5.28,1,1L14.78,12.72l-1-1Z"
                transform="translate(1.25 2.25)"
                fill={color}
            />
        </svg>
    );
};

export default ChatSearchIcon;