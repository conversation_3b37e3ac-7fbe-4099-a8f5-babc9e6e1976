import React from "react"

interface PackageBoxIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
}

const PackageBoxIcon: React.FC<PackageBoxIconProps> = ({
  size = 24,
  color = "#9da5b0",
  ...props
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M4.75 1.5h12V0h-12ZM20 4.75v12h1.5v-12ZM16.75 20h-12v1.5h12ZM1.5 16.75v-12H0v12ZM4.75 20A3.25 3.25 0 0 1 1.5 16.75H0A4.75 4.75 0 0 0 4.75 21.5ZM20 16.75A3.25 3.25 0 0 1 16.75 20v1.5a4.75 4.75 0 0 0 4.75-4.75ZM16.75 1.5A3.25 3.25 0 0 1 20 4.75h1.5A4.75 4.75 0 0 0 16.75 0ZM4.75 0A4.75 4.75 0 0 0 0 4.75H1.5A3.25 3.25 0 0 1 4.75 1.5Z"
      transform="translate(1.25 1.25)"
      fill={color}
    />
    <path
      d="M.75.75V0A.75.75 0 0 0 0 .75Zm8 0H9.5A.75.75 0 0 0 8.75 0ZM5.457 7.457l-.53.53h0Zm-1.414 0-.53-.53h0ZM2.457 9.043l.53.53h0Zm4.586 0 .53-.53h0ZM.75 1.5h8V0h-8ZM8 .75V8.336H9.5V.75ZM1.5 8.336V.75H0V8.336Zm6.073.177L5.987 6.927 4.927 7.987 6.513 9.573ZM3.513 6.927 1.927 8.513 2.987 9.573 4.573 7.987Zm2.475 0a1.75 1.75 0 0 0-2.475 0L4.573 7.987a.25.25 0 0 1 .354 0ZM0 8.336A1.75 1.75 0 0 0 2.987 9.573L1.927 8.513A.25.25 0 0 1 1.5 8.336Zm8 0a.25.25 0 0 1-.427.177L6.513 9.573A1.75 1.75 0 0 0 9.5 8.336Z"
      transform="translate(7.25 1.25)"
      fill={color}
    />
  </svg>
)

export default PackageBoxIcon
