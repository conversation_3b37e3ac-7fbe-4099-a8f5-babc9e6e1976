import React from "react"

interface FilterIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number
  color?: string
}

const FilterIcon: React.FC<FilterIconProps> = ({
  size = 24,
  color = "#5f6368",
  ...props
}) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M3 18.75a.75.75 0 0 0 1.5 0Zm1.5-5a.75.75 0 0 0-1.5 0Zm0-13A.75.75 0 0 0 3 .75Zm8.5 5a.75.75 0 0 0 1.5 0Zm1.5-5a.75.75 0 0 0-1.5 0Zm-1.5 18a.75.75 0 0 0 1.5 0Zm-8.5 0v-5H3v5Zm0-16v-2H3v2Zm10 3v-5H13v5Zm0 13v-2H13v2Zm-13-11v-2H0v2Zm4.5-2v2H7.5v-2Zm0 2A2.25 2.25 0 0 1 3.75 10v1.5A3.75 3.75 0 0 0 7.5 7.75ZM3.75 3.5A2.25 2.25 0 0 1 6 5.75H7.5A3.75 3.75 0 0 0 3.75 2ZM1.5 5.75A2.25 2.25 0 0 1 3.75 3.5V2A3.75 3.75 0 0 0 0 5.75ZM0 7.75A3.75 3.75 0 0 0 3.75 11.5V10A2.25 2.25 0 0 1 1.5 7.75Zm11.5 6v-2H10v2Zm4.5-2v2h1.5v-2Zm0 2A2.25 2.25 0 0 1 13.75 16v1.5a3.75 3.75 0 0 0 3.75-3.75ZM13.75 9.5A2.25 2.25 0 0 1 16 11.75h1.5A3.75 3.75 0 0 0 13.75 8ZM11.5 11.75A2.25 2.25 0 0 1 13.75 9.5V8A3.75 3.75 0 0 0 10 11.75Zm-1.5 2a3.75 3.75 0 0 0 3.75 3.75V16a2.25 2.25 0 0 1-2.25-2.25Z"
      transform="translate(3.25 2.25)"
      fill={color}
    />
  </svg>
)

export default FilterIcon
