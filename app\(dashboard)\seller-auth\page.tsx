import { BadgeCheck, IdCard, Info, Mail, Phone, Smartphone } from "lucide-react"
import Image from "next/image"
import Nationalcart from "@/public/assets/images/national_cart.png"
const SellerAuthPage = () => {
    return (
        <section className='bg-white p-5 px-10 max-md:px-3 border border-gray-300 rounded-2xl '>
            <div className='flex items-center gap-3'>
                <div className='bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3  rounded-b-4xl'>
                    <IdCard />
                </div>
                <h1 className='md:text-lg text-base'>قرارداد و امضای هوشمد</h1>
            </div>
            <div className='p-4 py-3  my-6 rounded-xl bg-orange-50 border border-warning border-dashed text-sm leading-6 text-gray-700'>
                <h2 className="text-base font-bold mb-5">
                    عنوان خطا یا پیغام و دلیل رد شدن
                </h2>
                <p className="text-base leading-8 max-md:text-sm text-justify">
                    لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ، و با استفاده از طراحان گرافیک است، چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است، و برای شرایط فعلی تکنولوژی مورد نیاز، و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد، کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد، تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی، و فرهنگ پیشرو در زبان فارسی ایجاد کرد،
                </p>
            </div>

            <div className="mt-8 grid md:grid-cols-2 ">
                <div className="relative md:w-[90%] max-md:h-52 max-md:mb-8">
                    <Image src={Nationalcart} alt="national_cart" fill className="w-full" />
                </div>
                <div className="flex flex-col md:gap-12 max-md:space-y-8">
                    <div className="flex flex-col gap-3 w-full">
                        <label htmlFor="" className="px-1 text-sm flex items-center gap-2"> تلفن موبایل  <Info size={20} className="bg-gray-400 text-white rounded-full" /></label>
                        <div className="relative w-full">
                            <input type="text" name='name' className='bg-gray-50 text-sm w-full px-12 border border-gray-300 rounded-xl p-3 py-5 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 ' />
                            <Smartphone className="absolute h-full top-0 right-2 border-l border-dashed border-gray-300 pl-3" size={34} />
                            <span className="text-xs flex items-center gap-1 flex-row-reverse bg-green-500 absolute rounded-md text-white p-1 left-3 top-5"> تایید شده <BadgeCheck size={18} /> </span>
                        </div>
                    </div>
                    <div className="flex flex-col gap-3 w-full">
                        <label htmlFor="" className="px-1 text-sm flex items-center gap-2"> تلفن ثابت  <Info size={20} className="bg-gray-400 text-white rounded-full" /></label>
                        <div className="relative w-full">
                            <input type="text" name='name' className='bg-gray-50 text-sm w-full px-12 border border-gray-300 rounded-xl p-3 py-5 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 ' />
                            <Phone className="absolute h-full top-0 right-2 border-l border-dashed border-gray-300 pl-3" size={34} />
                            <span className="text-xs flex items-center gap-1 flex-row-reverse bg-green-500 absolute rounded-md text-white p-1 left-3 top-5"> تایید شده <BadgeCheck size={18} /> </span>
                        </div>
                    </div>
                </div>
            </div>
            <div className="mt-8 md:grid grid-cols-2">
                <div className="md:w-[90%] max-md:mb-8">
                    <div className="flex flex-col gap-3 w-full">
                        <label htmlFor="" className="px-1 text-sm flex items-center gap-2"> کد ملی  <Info size={20} className="bg-gray-400 text-white rounded-full" /></label>
                        <div className="relative w-full">
                            <input type="text" name='name' className='bg-gray-50 text-sm w-full px-12 border border-gray-300 rounded-xl p-3 py-5 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 ' />
                            <IdCard className="absolute h-full top-0 right-2 border-l border-dashed border-gray-300 pl-3" size={34} />
                            <span className="text-xs flex items-center gap-1 flex-row-reverse bg-green-500 absolute rounded-md text-white p-1 left-3 top-5"> تایید شده <BadgeCheck size={18} /> </span>
                        </div>
                    </div>
                </div>
                <div className="flex flex-col gap-3 w-full ">
                    <label htmlFor="" className="px-1 text-sm flex items-center gap-2"> ایمیل  <Info size={20} className="bg-gray-400 text-white rounded-full" /></label>
                    <div className="border border-gray-300 rounded-2xl p-5 flex flex-col gap-5">
                        <div className="flex justify-between items-center">
                            <div className="flex gap-3 items-center">
                                <span className=" border-l border-dashed border-gray-400 pl-2.5">
                                    <Mail />
                                </span>
                                <span>
                                    <EMAIL>
                                </span>
                            </div>
                            <span className="text-warning">
                                تایید نشده
                            </span>
                        </div>
                        <div className="flex justify-between max-md:flex-wrap max-md:gap-y-5 items-center">
                            <input className="border w-40 max-md:w-full border-gray-300 border-dashed rounded-xl p-3 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 " type="text" />
                            <span>
                                ۲۹ : ۰۱ مانده تا دریافت مجدد کد
                            </span>
                            <button className="text-primary pr-3.5 border-r border-r-gray-400 cursor-pointer">
                                ارسال مجدد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}

export default SellerAuthPage